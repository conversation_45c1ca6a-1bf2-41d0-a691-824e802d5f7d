{"version": 3, "sources": ["../../src/decorator/Unique.ts"], "names": [], "mappings": ";;AA0CA,wBA+DC;AAzGD,wCAAmD;AAGnD,qDAAiD;AAoCjD;;GAEG;AACH,SAAgB,MAAM,CAClB,qBAImB,EACnB,oBAGmB,EACnB,YAA4B;IAE5B,MAAM,IAAI,GACN,OAAO,qBAAqB,KAAK,QAAQ;QACrC,CAAC,CAAC,qBAAqB;QACvB,CAAC,CAAC,SAAS,CAAA;IACnB,MAAM,MAAM,GACR,OAAO,qBAAqB,KAAK,QAAQ;QACrC,CAAC,CAGE,oBAAoB;QACvB,CAAC,CAAE,qBAAkC,CAAA;IAC7C,IAAI,OAAO,GACP,yBAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC;QAC3C,CAAC,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACjC,CAAC,CAAE,qBAAuC;QAC1C,CAAC,CAAC,YAAY,CAAA;IACtB,IAAI,CAAC,OAAO;QACR,OAAO;YACH,yBAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC;gBAC3C,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC;gBAChC,CAAC,CAAE,oBAAsC;gBACzC,CAAC,CAAC,YAAY,CAAA;IAE1B,OAAO,UACH,WAA8B,EAC9B,YAA8B;QAE9B,IAAI,OAAO,GAAG,MAAM,CAAA;QAEpB,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC7B,QAAQ,OAAO,YAAY,EAAE,CAAC;gBAC1B,KAAK,QAAQ;oBACT,OAAO,GAAG,CAAC,YAAY,CAAC,CAAA;oBACxB,MAAK;gBAET,KAAK,QAAQ;oBACT,OAAO,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAA;oBACnC,MAAK;YACb,CAAC;QACL,CAAC;QAED,MAAM,IAAI,GAAuB;YAC7B,MAAM,EAAE,YAAY;gBAChB,CAAC,CAAC,WAAW,CAAC,WAAW;gBACzB,CAAC,CAAE,WAAwB;YAC/B,IAAI,EAAE,IAAI;YACV,OAAO;YACP,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;SACvD,CAAA;QACD,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC/C,CAAC,CAAA;AACL,CAAC", "file": "Unique.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../globals\"\nimport { UniqueMetadataArgs } from \"../metadata-args/UniqueMetadataArgs\"\nimport { UniqueOptions } from \"./options/UniqueOptions\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\n\n/**\n * Composite unique constraint must be set on entity classes and must specify entity's fields to be unique.\n */\nexport function Unique(\n    name: string,\n    fields: string[],\n    options?: UniqueOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Composite unique constraint must be set on entity classes and must specify entity's fields to be unique.\n */\nexport function Unique(\n    fields: string[],\n    options?: UniqueOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Composite unique constraint must be set on entity classes and must specify entity's fields to be unique.\n */\nexport function Unique(\n    fields: (object?: any) => any[] | { [key: string]: number },\n    options?: UniqueOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Composite unique constraint must be set on entity classes and must specify entity's fields to be unique.\n */\nexport function Unique(\n    name: string,\n    fields: (object?: any) => any[] | { [key: string]: number },\n    options?: UniqueOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Composite unique constraint must be set on entity classes and must specify entity's fields to be unique.\n */\nexport function Unique(\n    nameOrFieldsOrOptions?:\n        | string\n        | string[]\n        | ((object: any) => any[] | { [key: string]: number })\n        | UniqueOptions,\n    maybeFieldsOrOptions?:\n        | ((object?: any) => any[] | { [key: string]: number })\n        | string[]\n        | UniqueOptions,\n    maybeOptions?: UniqueOptions,\n): ClassDecorator & PropertyDecorator {\n    const name =\n        typeof nameOrFieldsOrOptions === \"string\"\n            ? nameOrFieldsOrOptions\n            : undefined\n    const fields =\n        typeof nameOrFieldsOrOptions === \"string\"\n            ? <\n                  | ((object?: any) => any[] | { [key: string]: number })\n                  | string[]\n              >maybeFieldsOrOptions\n            : (nameOrFieldsOrOptions as string[])\n    let options =\n        ObjectUtils.isObject(nameOrFieldsOrOptions) &&\n        !Array.isArray(nameOrFieldsOrOptions)\n            ? (nameOrFieldsOrOptions as UniqueOptions)\n            : maybeOptions\n    if (!options)\n        options =\n            ObjectUtils.isObject(nameOrFieldsOrOptions) &&\n            !Array.isArray(maybeFieldsOrOptions)\n                ? (maybeFieldsOrOptions as UniqueOptions)\n                : maybeOptions\n\n    return function (\n        clsOrObject: Function | Object,\n        propertyName?: string | symbol,\n    ) {\n        let columns = fields\n\n        if (propertyName !== undefined) {\n            switch (typeof propertyName) {\n                case \"string\":\n                    columns = [propertyName]\n                    break\n\n                case \"symbol\":\n                    columns = [propertyName.toString()]\n                    break\n            }\n        }\n\n        const args: UniqueMetadataArgs = {\n            target: propertyName\n                ? clsOrObject.constructor\n                : (clsOrObject as Function),\n            name: name,\n            columns,\n            deferrable: options ? options.deferrable : undefined,\n        }\n        getMetadataArgsStorage().uniques.push(args)\n    }\n}\n"], "sourceRoot": ".."}