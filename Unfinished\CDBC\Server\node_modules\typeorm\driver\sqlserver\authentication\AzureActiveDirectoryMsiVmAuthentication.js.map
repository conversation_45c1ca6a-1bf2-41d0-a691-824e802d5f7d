{"version": 3, "sources": ["../../src/driver/sqlserver/authentication/AzureActiveDirectoryMsiVmAuthentication.ts"], "names": [], "mappings": "", "file": "AzureActiveDirectoryMsiVmAuthentication.js", "sourcesContent": ["export interface AzureActiveDirectoryMsiVmAuthentication {\n    type: \"azure-active-directory-msi-vm\"\n    options: {\n        /**\n         * If you user want to connect to an Azure app service using a specific client account\n         * they need to provide `clientId` associate to their created identity.\n         *\n         * This is optional for retrieve token from azure web app service\n         */\n        clientId?: string\n        /**\n         * A user need to provide `msiEndpoint` for retrieving the accesstoken.\n         */\n        msiEndpoint?: string\n    }\n}\n"], "sourceRoot": "../../.."}