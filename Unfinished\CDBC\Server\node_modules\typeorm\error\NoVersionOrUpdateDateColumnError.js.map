{"version": 3, "sources": ["../../src/error/NoVersionOrUpdateDateColumnError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,gCAAiC,SAAQ,2BAAY;IAC9D,YAAY,MAAc;QACtB,KAAK,CAAC,UAAU,MAAM,gDAAgD,CAAC,CAAA;IAC3E,CAAC;CACJ;AAJD,4EAIC", "file": "NoVersionOrUpdateDateColumnError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when an entity does not have no version and no update date column.\n */\nexport class NoVersionOrUpdateDateColumnError extends TypeORMError {\n    constructor(entity: string) {\n        super(`Entity ${entity} does not have version or update date columns.`)\n    }\n}\n"], "sourceRoot": ".."}