{"version": 3, "sources": ["../../src/driver/sap/SapQueryRunner.ts"], "names": [], "mappings": ";;;AAAA,+BAAgC;AAEhC,uCAA4D;AAC5D,iGAA6F;AAC7F,+FAA2F;AAC3F,uFAAmF;AAEnF,wEAAoE;AACpE,4DAAwD;AACxD,gEAA4D;AAG5D,4DAAwD;AACxD,sEAAkE;AAClE,wEAAoE;AAEpE,gFAA4E;AAC5E,sEAAkE;AAClE,wEAAoE;AACpE,yDAAqD;AACrD,8DAA0D;AAC1D,0EAAsE;AACtE,gEAA4D;AAC5D,kDAA8C;AAC9C,oCAAgC;AAGhC,kEAA8D;AAI9D;;GAEG;AACH,MAAa,cAAe,SAAQ,iCAAe;IAqB/C,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAiB,EAAE,IAAqB;QAChD,KAAK,EAAE,CAAA;QAPH,SAAI,GAAc,IAAI,qBAAS,EAAE,CAAA;QAQrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,kBAAkB;YAAE,OAAO,IAAI,CAAC,kBAAkB,CAAA;QAE3D,IAAI,CAAC,kBAAkB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAA;QAEpE,OAAO,IAAI,CAAC,kBAAkB,CAAA;IAClC,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QAEtB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QAC9D,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,IACI,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,MAAM,CAAC,kBAAkB,KAAK,QAAQ;YAE3C,MAAM,IAAI,+DAA8B,EAAE,CAAA;QAE9C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAE1D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAE/B;;;WAGG;QACH,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;QAE3C,IAAI,cAAc,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,KAAK,CACZ,mCAAmC,cAAc,IAAI,EAAE,EAAE,CAC5D,CAAA;QACL,CAAC;QAED,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC1B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QAEhC,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QAC5B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QAEhC,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CAAC,OAAiC;QACjD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAEvC,MAAM,OAAO,GAAG,IAAA,gBAAS,EAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAA;QAE3D,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,CAAA;QAEjD,MAAM,KAAK,GAAG,kCAAkC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,GAAG,CAAA;QAC/E,IAAI,CAAC;YACD,MAAM,OAAO,CAAC,KAAK,CAAC,CAAA;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,wBAAgB,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,CAAA;QAChD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QAEzC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,SAAc,CAAA;QAClB,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;QAEhC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QAEjD,IAAI,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACjC,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,aAAa,CAAA;YAE3D,IAAI,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,SAAS,GAAG,MAAM,IAAA,gBAAS,EAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,IAAI,CACxD,kBAAkB,EAClB,KAAK,CACR,CAAA;YACL,CAAC;YAED,IAAI,GAAQ,CAAA;YACZ,IAAI,CAAC;gBACD,GAAG,GAAG,SAAS;oBACX,CAAC,CAAC,MAAM,IAAA,gBAAS,EAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAChC,SAAS,EACT,UAAU,CACb;oBACH,CAAC,CAAC,MAAM,IAAA,gBAAS,EAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,IAAI,CACzC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,EAAE,CACL,CAAA;YACX,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,MAAM,IAAI,wBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;YACtD,CAAC;YAED,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,qBAAqB,CAAA;YACxD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YAExD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;YAED,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB,EAC5C,CAAC;gBACC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACL,CAAC;YAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC1B,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAA;YACzB,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,CAAC,OAAO,GAAG,GAAG,CAAA;YACxB,CAAC;YAED,MAAM,CAAC,GAAG,GAAG,GAAG,CAAA;YAEhB,IAAI,aAAa,EAAE,CAAC;gBAChB,MAAM,WAAW,GAAG,oDAAoD,CAAA;gBACxE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;gBAC7D,MAAM,mBAAmB,GAAG,MAAM,IAAI,OAAO,CACzC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;oBACT,kBAAkB,CAAC,IAAI,CACnB,WAAW,EACX,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE,CACnB,GAAG;wBACC,CAAC,CAAC,IAAI,CACA,IAAI,wBAAgB,CAChB,WAAW,EACX,EAAE,EACF,GAAG,CACN,CACJ;wBACH,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CACpB,CAAA;gBACL,CAAC,CACJ,CAAA;gBAED,MAAM,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAA;gBAC/D,MAAM,CAAC,OAAO,GAAG,mBAAmB,CAAA;YACxC,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;YACD,MAAM,GAAG,CAAA;QACb,CAAC;gBAAS,CAAC;YACP,iDAAiD;YACjD,IAAI,SAAS,EAAE,IAAI,EAAE,CAAC;gBAClB,MAAM,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;YAC/D,CAAC;YAED,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAE9B,2BAA2B;YAC3B,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,mBAAmB,EAAE,CAAC;YACtB,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,OAAO,MAAM,CAAC,GAAG,CAAA;QACrB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QACzC,IAAI,SAAc,CAAA;QAClB,IAAI,SAAc,CAAA;QAElB,MAAM,OAAO,GAAG,KAAK,IAAI,EAAE;YACvB,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAA,gBAAS,EAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACpD,CAAC;YACD,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAA,gBAAS,EAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnD,CAAC;YACD,OAAO,EAAE,CAAA;QACb,CAAC,CAAA;QAED,IAAI,CAAC;YACD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;YAC/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAE/D,SAAS,GAAG,MAAM,IAAA,gBAAS,EAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,IAAI,CACxD,kBAAkB,EAClB,KAAK,CACR,CAAA;YACD,SAAS,GAAG,MAAM,IAAA,gBAAS,EAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CACpD,SAAS,EACT,UAAU,CACb,CAAA;YAED,MAAM,MAAM,GACR,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAA;YAC1D,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE;gBACxB,MAAM,OAAO,EAAE,CAAA;gBACf,KAAK,EAAE,EAAE,CAAA;YACb,CAAC,CAAC,CAAA;YACF,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAY,EAAE,EAAE;gBACtC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,KAAK,EACL,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;gBACD,MAAM,OAAO,EAAE,CAAA;gBACf,OAAO,EAAE,CAAC,KAAK,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;YAEF,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,KAAK,EACL,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,MAAM,OAAO,EAAE,CAAA;YACf,MAAM,IAAI,wBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;QACxD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,MAAM,OAAO,GAAoB,MAAM,IAAI,CAAC,KAAK,CAC7C,+CAA+C,CAClD,CAAA;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,MAAM,KAAK,GAAG,QAAQ;YAClB,CAAC,CAAC,kBAAkB,QAAQ,mBAAmB;YAC/C,CAAC,CAAC,+BAA+B,CAAA;QACrC,MAAM,OAAO,GAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACxD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;QAC3C,OAAO,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,cAAc,GAAyB,MAAM,IAAI,CAAC,KAAK,CACzD,4DAA4D,CAC/D,CAAA;QAED,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB;QAIvB,MAAM,cAAc,GAChB,MAAM,IAAI,CAAC,KAAK,CACZ,uFAAuF,CAC1F,CAAA;QAEL,OAAO,cAAc,CAAC,CAAC,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;QACvC,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,kBAAkB,GAA6B,MAAM,IAAI,CAAC,KAAK,CACjE,0DAA0D,CAC7D,CAAA;QAED,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,4EAA4E,eAAe,CAAC,MAAM,yBAAyB,eAAe,CAAC,SAAS,GAAG,CAAA;QACnK,MAAM,MAAM,GAA2B,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE5D,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,oFAAoF,eAAe,CAAC,MAAM,yBAAyB,eAAe,CAAC,SAAS,0BAA0B,UAAU,GAAG,CAAA;QAC/M,MAAM,MAAM,GAA4B,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE7D,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,MAAM,MAAM,GACR,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAElC,IAAI,KAAK,GAAG,KAAK,CAAA;QACjB,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,wDAAwD,MAAM,GAAG,CACpE,CAAA;YACD,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAA;QAC3B,CAAC;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACxC,MAAM,EAAE,GAAG,kBAAkB,MAAM,GAAG,CAAA;YACtC,MAAM,IAAI,GAAG,gBAAgB,MAAM,WAAW,CAAA;YAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;QAC7D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,UAAkB,EAClB,OAAiB,EACjB,SAAmB;QAEnB,MAAM,MAAM,GACR,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAClC,IAAI,KAAK,GAAG,KAAK,CAAA;QACjB,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,wDAAwD,MAAM,GAAG,CACpE,CAAA;YACD,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAA;QAC3B,CAAC;QACD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,EAAE,GAAG,gBAAgB,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;YAClE,MAAM,IAAI,GAAG,kBAAkB,MAAM,GAAG,CAAA;YACxC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;QAC7D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI,EACjC,gBAAyB,IAAI;QAE7B,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QACD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,iFAAiF;QACjF,kIAAkI;QAClI,IAAI,iBAAiB;YACjB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC9D,CAAA;QAEL,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,sFAAsF;gBACtF,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,OAAiB,EACjB,kBAA2B,IAAI,EAC/B,cAAuB,IAAI;QAE3B,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;YACrD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,qGAAqG;QACrG,wDAAwD;QAExD,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,iGAAiG;QACjG,kIAAkI;QAClI,IAAI,eAAe;YACf,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC5D,CAAA;QAEL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,IAAU,EACV,mBAA4B,KAAK;QAEjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,iCAAe,CAAC,OAAO,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,GACjD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAExC,QAAQ,CAAC,IAAI,GAAG,UAAU;YACtB,CAAC,CAAC,GAAG,UAAU,IAAI,YAAY,EAAE;YACjC,CAAC,CAAC,YAAY,CAAA;QAElB,eAAe;QACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,gBAAgB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,UAAU,CAC3D,QAAQ,CACX,EAAE,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,gBAAgB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,UAAU,CAC3D,QAAQ,CACX,EAAE,CACN,CACJ,CAAA;QAED,kFAAkF;QAClF,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAA;YAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAA;QACpE,CAAC,CAAC,CAAA;QAEF,4EAA4E;QAC5E,mFAAmF;QACnF,MAAM,uBAAuB,GAAG,mFAAmF,UAAU,oCAAoC,YAAY,GAAG,CAAA;QAChL,MAAM,aAAa,GAAoB,MAAM,IAAI,CAAC,KAAK,CACnD,uBAAuB,CAC1B,CAAA;QACD,IAAI,qBAAqB,GAAsB,EAAE,CAAA;QACjD,MAAM,gCAAgC,GAGhC,EAAE,CAAA;QACR,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBACvD,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBAED,gCAAgC,CAAC,IAAI,CAAC;oBAClC,SAAS,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE;oBACzE,MAAM,EAAE,YAAY,CAAC,iBAAiB,CAAC;iBAC1C,CAAC,CAAA;gBACF,OAAO,IAAI,iCAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC3D,kBAAkB,EAAE,QAAQ,CAAC,QAAQ;oBACrC,gBAAgB,EAAE,QAAQ,CAAC,MAAM;oBACjC,mBAAmB,EAAE,QAAQ,CAAC,IAAI,EAAE,4BAA4B;oBAChE,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,gEAAgE;iBAC7H,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,+BAA+B;YAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;gBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAC3D,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,gCAAgC;QAChC,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;YACD,MAAM,iBAAiB,GAAG,WAAW;iBAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;iBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YAED,cAAc;YACd,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,qBAAqB,SAAS,GAAG,CACrC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,oBAAoB,SAAS,kBAAkB,iBAAiB,GAAG,CACvE,CACJ,CAAA;YAED,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,oBAAoB,SAAS,kBAAkB,iBAAiB,GAAG,CACvE,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,qBAAqB,SAAS,GAAG,CACrC,CACJ,CAAA;QACL,CAAC;QAED,kDAAkD;QAClD,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,0BAA0B;YAC1B,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAED,kBAAkB;YAClB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAA;YAC9D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;QAEF,kCAAkC;QAClC,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;YACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAC3D,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,4BAA4B;YAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,iBAAiB;YACjB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;YAClD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;YAEtD,0BAA0B;YAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;YAEzB,mBAAmB;YACnB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;YACpD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,oDAAoD;QACpD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;QAC3D,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;QAE9D,0CAA0C;QAC1C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;YACjD,wEAAwE;YACxE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,4EAA4E;gBAC5E,mFAAmF;gBACnF,MAAM,uBAAuB,GAAG,mFAAmF,eAAe,CAAC,MAAM,oCAAoC,eAAe,CAAC,SAAS,GAAG,CAAA;gBACzM,MAAM,aAAa,GAAoB,MAAM,IAAI,CAAC,KAAK,CACnD,uBAAuB,CAC1B,CAAA;gBACD,IAAI,qBAAqB,GAAsB,EAAE,CAAA;gBACjD,MAAM,gCAAgC,GAGhC,EAAE,CAAA;gBACR,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,qBAAqB,GAAG,aAAa,CAAC,GAAG,CACrC,CAAC,YAAY,EAAE,EAAE;wBACb,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;4BACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;wBAED,gCAAgC,CAAC,IAAI,CAAC;4BAClC,SAAS,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE;4BACzE,MAAM,EAAE,YAAY,CAAC,iBAAiB,CAAC;yBAC1C,CAAC,CAAA;wBACF,OAAO,IAAI,iCAAe,CAAC;4BACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;4BACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;4BACD,kBAAkB,EAAE,KAAK,CAAC,QAAQ;4BAClC,gBAAgB,EAAE,KAAK,CAAC,MAAM;4BAC9B,mBAAmB,EAAE,KAAK,CAAC,IAAI;4BAC/B,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;4BACD,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;gCACtC,CAAC,CAAC,WAAW;gCACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;4BACrC,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;gCACtC,CAAC,CAAC,WAAW;gCACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;4BACrC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,CAC1C,GAAG,EACH,GAAG,CACN;yBACJ,CAAC,CAAA;oBACN,CAAC,CACJ,CAAA;oBAED,+BAA+B;oBAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;wBACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;wBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAClB,OAAQ,CAAC,SAAS,EAClB,UAAU,CACb,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,mBAAmB,CACpB,OAAQ,CAAC,SAAS,EAClB,UAAU,CACb,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;gBACN,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACxD,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;gBACD,MAAM,WAAW,GAAG,cAAc;qBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;qBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBAED,kCAAkC;gBAClC,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,mBAAmB,CACpB,OAAQ,CAAC,SAAS,EAClB,UAAU,CACb,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACxD,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YACD,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QACL,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,IAAI,uBAAU,CAAC;gBAC/B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;oBAClD,MAAM,CAAC,IAAI;iBACd,CAAC;gBACF,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC1B,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAA;YACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,WAAW,CAAC,OAAO,CAAC,IAAI,CACpB,IAAI,yBAAW,CAAC;gBACZ,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;aACvC,CAAC,CACL,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IAAI,SAAS,GAA4B,SAAS,CAAA;QAClD,IAAI,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAoB,CACnD,CAAA;QACP,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IACI,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW;YAC5C,SAAS,CAAC,kBAAkB,KAAK,MAAM,CAAC;YAC5C,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EACvC,CAAC;YACC,yGAAyG;YACzG,kDAAkD;YAClD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAEtC,sBAAsB;YACtB,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACpC,gBAAgB;gBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACnC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACnC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC/B,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;oBAEjD,oCAAoC;oBACpC,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;oBACD,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,+CAA+C;oBAC/C,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC1D,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAChC,MAAM,iBAAiB,GAAG,WAAW;yBAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;yBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEf,cAAc;oBACd,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,SAAS,GAAG,CACrC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,SAAS,kBAAkB,iBAAiB,GAAG,CACvE,CACJ,CAAA;oBAED,oCAAoC;oBACpC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,SAAS,kBAAkB,iBAAiB,GAAG,CACvE,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,SAAS,GAAG,CACrC,CACJ,CAAA;gBACL,CAAC;gBAED,2BAA2B;gBAC3B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvD,4BAA4B;oBAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CACpB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EACzC,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACtC,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,iBAAiB;oBACjB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAA;oBACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAA;oBAEzD,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;oBAEzB,mBAAmB;oBACnB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAA;oBACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAA;gBAC3D,CAAC,CAAC,CAAA;gBAEF,iCAAiC;gBACjC,WAAW;qBACN,qBAAqB,CAAC,SAAS,CAAC;qBAChC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACpB,4BAA4B;oBAC5B,UAAU,CAAC,WAAW,CAAC,MAAM,CACzB,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAC3C,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,CAAC,CAClD,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,UAAU,CAAC,CACpD,CAAA;oBAED,0BAA0B;oBAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;oBAEnC,kBAAkB;oBAClB,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,UAAU,CAAC,CACpD,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,CAAC,CAClD,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEN,2BAA2B;gBAC3B,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACtD,4BAA4B;oBAC5B,KAAK,CAAC,WAAY,CAAC,MAAM,CACrB,KAAK,CAAC,WAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC1C,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACvC,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,WAAW,EACX,KAAK,CAAC,UAAW,CACpB,CAAA;oBAEL,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,KAAK,CAAC,CAClD,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,KAAK,CAAC,CACpD,CAAA;oBAED,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;oBAEzB,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,KAAK,CAAC,CACpD,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,KAAK,CAAC,CAClD,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,wCAAwC;gBACxC,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;gBACD,WAAW,CAAC,OAAO,CACf,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAe,CAAC,CAC/C,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;gBACvB,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;YACnC,CAAC;YAED,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC;gBACnD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,WAAW,IAAI,CAAC,oBAAoB,CACjC,SAAS,EACT,CAAC,CACG,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,CAClC,EACD,CAAC,SAAS,CAAC,UAAU,CACxB,GAAG,CACP,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,WAAW,IAAI,CAAC,oBAAoB,CACjC,SAAS,EACT,CAAC,CACG,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,CAClC,EACD,CAAC,SAAS,CAAC,UAAU,CACxB,GAAG,CACP,CACJ,CAAA;YACL,CAAC;iBAAM,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBACjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,SAAS,CAAC,IACd,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAClD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,SAAS,CAAC,IACd,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAClD,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC9C,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;gBAEjD,2EAA2E;gBAC3E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,MAAM,GACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBACL,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC/B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAC9B,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,IAAI,CAAA;oBACxB,MAAM,MAAM,GACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBACL,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CACnC,CAAA;oBACD,cAAc,CAAC,MAAM,CACjB,cAAc,CAAC,OAAO,CAAC,aAAc,CAAC,EACtC,CAAC,CACJ,CAAA;oBAED,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,KAAK,CAAA;oBAEzB,gEAAgE;oBAChE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,MAAM,MAAM,GACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;wBACL,MAAM,WAAW,GAAG,cAAc;6BAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;6BACnC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBAC9B,MAAM,WAAW,GAAG,IAAI,uBAAU,CAAC;wBAC/B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;4BAClD,SAAS,CAAC,IAAI;yBACjB,CAAC;wBACF,WAAW,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;wBAC7B,QAAQ,EAAE,IAAI;qBACjB,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;oBACrC,WAAW,CAAC,OAAO,CAAC,IAAI,CACpB,IAAI,yBAAW,CAAC;wBACZ,IAAI,EAAE,WAAW,CAAC,IAAI;wBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;qBACvC,CAAC,CACL,CAAA;oBACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;oBACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;gBAC3D,CAAC;qBAAM,CAAC;oBACJ,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACnD,OAAO,CACH,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;4BAC9B,KAAK,CAAC,QAAQ,KAAK,IAAI;4BACvB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CACpB,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,CAChD,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAY,CAAC,EACzC,CAAC,CACJ,CAAA;oBAED,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,WAAY,CAAC,IAAI,CAChD,CAAA;oBACD,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAY,CAAC,EACzC,CAAC,CACJ,CAAA;oBAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAY,CAAC,CAAC,CAAA;oBACtD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAY,CAAC,CAAC,CAAA;gBAC9D,CAAC;YACL,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;YACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAC/C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,KAAK,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,MAAM,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,oBAAY,CAClB,WAAW,YAAY,6BAA6B,KAAK,CAAC,IAAI,GAAG,CACpE,CAAA;QAEL,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,8BAA8B;QAC9B,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,4EAA4E;YAC5E,mFAAmF;YACnF,MAAM,uBAAuB,GAAG,mFAAmF,eAAe,CAAC,MAAM,oCAAoC,eAAe,CAAC,SAAS,GAAG,CAAA;YACzM,MAAM,aAAa,GAAoB,MAAM,IAAI,CAAC,KAAK,CACnD,uBAAuB,CAC1B,CAAA;YACD,IAAI,qBAAqB,GAAsB,EAAE,CAAA;YACjD,MAAM,gCAAgC,GAGhC,EAAE,CAAA;YACR,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;oBACvD,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;wBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;oBAED,gCAAgC,CAAC,IAAI,CAAC;wBAClC,SAAS,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE;wBACzE,MAAM,EAAE,YAAY,CAAC,iBAAiB,CAAC;qBAC1C,CAAC,CAAA;oBACF,OAAO,IAAI,iCAAe,CAAC;wBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;wBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;wBACD,kBAAkB,EAAE,KAAK,CAAC,QAAQ;wBAClC,gBAAgB,EAAE,KAAK,CAAC,MAAM;wBAC9B,mBAAmB,EAAE,KAAK,CAAC,IAAI;wBAC/B,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;wBACD,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;4BACtC,CAAC,CAAC,WAAW;4BACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;wBACrC,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;4BACtC,CAAC,CAAC,WAAW;4BACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;wBACrC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,CAC1C,GAAG,EACH,GAAG,CACN;qBACJ,CAAC,CAAA;gBACN,CAAC,CAAC,CAAA;gBAEF,+BAA+B;gBAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,mBAAmB,CACpB,OAAQ,CAAC,SAAS,EAClB,UAAU,CACb,CACJ,CAAA;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACxD,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC1D,CAAA;YACD,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;iBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;iBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YAED,yBAAyB;YACzB,MAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC7D,WAAY,CAAC,SAAS,GAAG,KAAK,CAAA;YAE9B,mFAAmF;YACnF,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACxD,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC1D,CAAA;gBACD,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;qBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;qBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACL,CAAC;YAED,kCAAkC;YAClC,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;gBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAC3D,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC7D,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACzB,6DAA6D;YAC7D,MAAM,UAAU,GACZ,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE;gBACvD,MAAM,CAAC,IAAI;aACd,CAAC,CAAA;YACN,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,UAAU,CACzC,CAAA;YACD,IAAI,WAAW,EAAE,CAAC;gBACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;gBACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAA;gBACpD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,wBAAwB,UAAU,QAAQ,IAAI,CAAC,UAAU,CACrD,KAAK,CACR,MAAM,MAAM,CAAC,IAAI,IAAI,CACzB,CACJ,CAAA;YACL,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;gBAC9D,MAAM,CAAC,IAAI;aACd,CAAC,CAAA;YACF,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CACtC,CAAA;YACD,IAAI,UAAU,EAAE,CAAC;gBACb,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EACvC,CAAC,CACJ,CAAA;gBACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAA;gBACnD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,wBAAwB,SAAS,QAAQ,IAAI,CAAC,UAAU,CACpD,KAAK,CACR,MAAM,MAAM,CAAC,IAAI,IAAI,CACzB,CACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CACN,CAAC,CAAC,KAAK,CAAC,WAAW;YACnB,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,MAAM,CAAC,MAAM,CACrB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EACvC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QACvE,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;QAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;QAE7D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB;QAErB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAEvD,4GAA4G;QAC5G,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QAEhD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACxD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,4EAA4E;QAC5E,mFAAmF;QACnF,MAAM,uBAAuB,GAAG,mFAAmF,eAAe,CAAC,MAAM,oCAAoC,eAAe,CAAC,SAAS,GAAG,CAAA;QACzM,MAAM,aAAa,GAAoB,MAAM,IAAI,CAAC,KAAK,CACnD,uBAAuB,CAC1B,CAAA;QACD,IAAI,qBAAqB,GAAsB,EAAE,CAAA;QACjD,MAAM,gCAAgC,GAGhC,EAAE,CAAA;QACR,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBACvD,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBAED,gCAAgC,CAAC,IAAI,CAAC;oBAClC,SAAS,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE;oBACzE,MAAM,EAAE,YAAY,CAAC,iBAAiB,CAAC;iBAC1C,CAAC,CAAA;gBACF,OAAO,IAAI,iCAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC3D,kBAAkB,EAAE,KAAK,CAAC,QAAQ;oBAClC,gBAAgB,EAAE,KAAK,CAAC,MAAM;oBAC9B,mBAAmB,EAAE,KAAK,CAAC,IAAI;oBAC/B,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;iBAC3D,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,+BAA+B;YAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;gBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAC3D,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,4DAA4D;QAC5D,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;QACjD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACxD,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YACD,MAAM,iBAAiB,GAAG,cAAc;iBACnC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACL,CAAC;QAED,2BAA2B;QAC3B,WAAW,CAAC,OAAO;aACd,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAEnD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACxD,WAAW,EACX,WAAW,CACd,CAAA;QACD,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QAED,kCAAkC;QAClC,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;YACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAC3D,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,WAA2B;QAC5C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,4EAA4E;QAC5E,mFAAmF;QACnF,MAAM,uBAAuB,GAAG,mFAAmF,eAAe,CAAC,MAAM,oCAAoC,eAAe,CAAC,SAAS,GAAG,CAAA;QACzM,MAAM,aAAa,GAAoB,MAAM,IAAI,CAAC,KAAK,CACnD,uBAAuB,CAC1B,CAAA;QACD,IAAI,qBAAqB,GAAsB,EAAE,CAAA;QACjD,MAAM,gCAAgC,GAGhC,EAAE,CAAA;QACR,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBACvD,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBAED,gCAAgC,CAAC,IAAI,CAAC;oBAClC,SAAS,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE;oBACzE,MAAM,EAAE,YAAY,CAAC,iBAAiB,CAAC;iBAC1C,CAAC,CAAA;gBACF,OAAO,IAAI,iCAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC3D,kBAAkB,EAAE,KAAK,CAAC,QAAQ;oBAClC,gBAAgB,EAAE,KAAK,CAAC,MAAM;oBAC9B,mBAAmB,EAAE,KAAK,CAAC,IAAI;oBAC/B,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;iBAC3D,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,+BAA+B;YAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;gBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAC3D,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAA;QAC7C,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,mBAAmB,CACpB,KAAK,EACL,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CACpD,CACJ,CAAA;QAED,kCAAkC;QAClC,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACzC,MAAM,OAAO,GAAG,gCAAgC,CAAC,IAAI,CACjD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CACxC,CAAA;YACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAC3D,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,OAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CACzD,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,IAAI,oBAAY,CAClB,yEAAyE,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,oBAAY,CAClB,yEAAyE,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,IAAI,oBAAY,CAClB,yEAAyE,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,oBAAY,CAClB,yEAAyE,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,eAAe,CAAC,IAAI;YACrB,eAAe,CAAC,IAAI;gBAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,eAAe,CAAC,UAAW,CAC9B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,eAAe,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YAC7D,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,oBAAY,CAClB,oDAAoD,KAAK,CAAC,IAAI,EAAE,CACnE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CACzD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,oBAAY,CAClB,kDAAkD,CACrD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAClB,kDAAkD,CACrD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,oBAAY,CAClB,kDAAkD,CACrD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAClB,kDAAkD,CACrD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,gFAAgF;QAChF,IAAI,CAAC,UAAU,CAAC,IAAI;YAChB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CACjD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,UAAU,GAAG,iCAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAClE,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,oBAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,CAC/C,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CACvC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,KAAK,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,oBAAY,CAClB,kBAAkB,WAAW,2BAA2B,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QAEL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CACrC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACf,MAAM,OAAO,GAAa,EAAE,CAAA;QAC5B,IAAI,CAAC,UAAU,CAAC,eAAe;aAC1B,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;aACrC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAClB,MAAM,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAChC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CACzC,CAAA;YACD,IAAI,CAAC,aAAa;gBAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAO,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;QAEN,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,gBAAgB,CAAC,CAAA;QAC5D,MAAM,iBAAiB,GAAG,OAAO;aAC5B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,OAAO,IAAI,KAAK,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,CAAA;QAC9D,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,iIAAiI;YACjI,4KAA4K;YAC5K,mFAAmF;YACnF,uEAAuE;YAEvE,kEAAkE;YAClE,MAAM,qBAAqB,GAAG,sIAAsI,iBAAiB,2FAA2F,CAAA;YAChR,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,qBAAqB,CACxB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACtD,CAAA;YAED,IAAI,CAAC,0BAA0B;gBAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B;oBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACxC,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,EAAE,CAAA;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACvD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEnD,MAAM,cAAc,GAAG,SAAS;aAC3B,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACd,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;YAExC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,aAAa,CAAA;YAC1B,CAAC;YAED,OAAO,oBAAoB,MAAM,uBAAuB,IAAI,IAAI,CAAA;QACpE,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,MAAM,KAAK,GAAG,qBAAqB,IAAI,CAAC,UAAU,CAC9C,IAAI,CAAC,2BAA2B,EAAE,CACrC,4BAA4B,qCAAiB,CAAC,IAAI,KAC/C,cAAc,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAG,CAAC,CAAC,CAAC,EACjD,EAAE,CAAA;QACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACvC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;YAC/B,MAAM,IAAI,GAAG,IAAI,WAAI,EAAE,CAAA;YACvB,MAAM,MAAM,GACR,MAAM,CAAC,QAAQ,CAAC,KAAK,aAAa;gBAClC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;gBACvB,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAA;YAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;YAC9D,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;YACjC,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEvD,MAAM,QAAQ,GAAkD,EAAE,CAAA;QAElE,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,wDAAwD,CAAA;YAE1E,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,eAAe,GAAG,UAAU;iBAC7B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;gBACf,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBACzC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,IAAI,GAAG,MAAM,CAAA;oBACb,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAA;gBACxD,CAAC;gBACD,OAAO,qBAAqB,MAAM,yBAAyB,IAAI,IAAI,CAAA;YACvE,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,CAAC,CAAA;YAEjB,MAAM,SAAS,GACX,+DAA+D;gBAC/D,eAAe,CAAA;YAEnB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,yDAAyD;QACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAA;QAEpC,MAAM,gBAAgB,GAAG,QAAQ;aAC5B,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,EAAE;YACjC,OAAO,qBAAqB,WAAW,yBAAyB,UAAU,IAAI,CAAA;QAClF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,MAAM,UAAU,GACZ,4CAA4C;YAC5C,gBAAgB;YAChB,sBAAsB,CAAA;QAE1B,MAAM,oBAAoB,GAAG,QAAQ;aAChC,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,EAAE;YACjC,OAAO,qBAAqB,WAAW,yBAAyB,UAAU,IAAI,CAAA;QAClF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,MAAM,cAAc,GAAG,4CAA4C,oBAAoB,uBAAuB,CAAA;QAE9G,MAAM,gBAAgB,GAAG,QAAQ;aAC5B,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,EAAE;YACjC,OAAO,yBAAyB,WAAW,6BAA6B,UAAU,IAAI,CAAA;QAC1F,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,2DAA2D;QAC3D,MAAM,UAAU,GACZ,uHAAuH;YACvH,uGAAuG;YACvG,UAAU,gBAAgB,iJAAiJ,CAAA;QAE/K,MAAM,oBAAoB,GAAG,QAAQ;aAChC,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,EAAE;YACjC,OAAO,qBAAqB,WAAW,yBAAyB,UAAU,IAAI,CAAA;QAClF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,MAAM,cAAc,GAAG,wDAAwD,oBAAoB,uBAAuB,CAAA;QAC1H,MAAM,CACF,SAAS,EACT,aAAa,EACb,SAAS,EACT,aAAa,EAChB,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;SAC7B,CAAC,CAAA;QAEF,kCAAkC;QAClC,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,aAAK,EAAE,CAAA;YACzB,MAAM,gBAAgB,GAAG,CAAC,QAAa,EAAE,GAAW,EAAE,EAAE;gBACpD,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,aAAa;oBAClC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;wBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC;oBACjD,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YACvB,CAAC,CAAA;YAED,mEAAmE;YACnE,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAA;YACvD,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAA;YAChC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;YACrC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CACnC,OAAO,CAAC,YAAY,CAAC,EACrB,MAAM,CACT,CAAA;YAED,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS;iBACJ,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,YAAY,CAAC;gBAClB,OAAO,CAAC,YAAY,CAAC;gBACzB,QAAQ,CAAC,aAAa,CAAC;oBACnB,OAAO,CAAC,aAAa,CAAC,CACjC;iBACA,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAC1C,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,YAAY,CAAC;oBACtB,QAAQ,CAAC,YAAY,CAAC;oBAC1B,YAAY,CAAC,aAAa,CAAC;wBACvB,QAAQ,CAAC,aAAa,CAAC;oBAC3B,YAAY,CAAC,aAAa,CAAC;wBACvB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBAED,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CACxC,CAAC,OAAO,EAAE,EAAE;oBACR,OAAO,CACH,OAAO,CAAC,YAAY,CAAC;wBACjB,OAAO,CAAC,YAAY,CAAC;wBACzB,OAAO,CAAC,aAAa,CAAC;4BAClB,OAAO,CAAC,aAAa,CAAC;wBAC1B,OAAO,CAAC,aAAa,CAAC;4BAClB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,OAAO,CAAC,YAAY,CAAC;wBACrB,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CACzB,QAAQ,CACX,KAAK,CAAC,CAAC,CACX,CAAA;gBACL,CAAC,CACJ,CAAA;gBAED,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAChC,CAAC,QAAQ,EAAE,EAAE,CACT,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;oBACxB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAClC,CAAA;gBACL,MAAM,eAAe,GACjB,mBAAmB,CAAC,MAAM,GAAG,CAAC;oBAC9B,aAAa;oBACb,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACjC,OAAO,mBAAmB,CAAC,IAAI,CAC3B,CAAC,WAAW,EAAE,EAAE;4BACZ,OAAO,CACH,KAAK,CAAC,IAAI;gCACN,WAAW,CAAC,YAAY,CAAC;gCAC7B,KAAK,CAAC,WAAW,KAAK,KAAK,CAC9B,CAAA;wBACL,CAAC,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,qBAAqB,GACvB,mBAAmB,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;oBACtC,OAAO,SAAS,CAAC,IAAI,CACjB,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC;wBACjB,WAAW,CAAC,YAAY,CAAC;wBAC7B,OAAO,CAAC,aAAa,CAAC;4BAClB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAC1C,WAAW,CAAC,IAAI;oBACZ,QAAQ,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAA;gBAE5C,IACI,WAAW,CAAC,IAAI,KAAK,KAAK;oBAC1B,WAAW,CAAC,IAAI,KAAK,SAAS,EAChC,CAAC;oBACC,wGAAwG;oBACxG,iFAAiF;oBACjF,IACI,QAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI;wBAC3B,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,QAAQ,CAAC,CACrB,EACH,CAAC;wBACC,WAAW,CAAC,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAA;oBAC9C,CAAC;yBAAM,IACH,QAAQ,CAAC,OAAO,CAAC,KAAK,IAAI;wBAC1B,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,OAAO,CAAC,CACpB,EACH,CAAC;wBACC,WAAW,CAAC,SAAS,GAAG,SAAS,CAAA;oBACrC,CAAC;oBACD,IACI,QAAQ,CAAC,OAAO,CAAC,KAAK,IAAI;wBAC1B,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,OAAO,CAAC,CACpB,EACH,CAAC;wBACC,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAA;oBACzC,CAAC;yBAAM,IACH,QAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI;wBAC3B,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,QAAQ,CAAC,CACrB,EACH,CAAC;wBACC,WAAW,CAAC,KAAK,GAAG,SAAS,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,IACI,QAAQ,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE;oBACxC,OAAO,EACT,CAAC;oBACC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAA;oBAC1B,WAAW,CAAC,IAAI;wBACZ,QAAQ,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAA;gBACnD,CAAC;gBAED,+CAA+C;gBAC/C,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CACrC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC;oBACR,QAAQ,CAAC,QAAQ,CAAC,EACpB,CAAC;oBACC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAA;oBAC5C,WAAW,CAAC,MAAM;wBACd,CAAC,IAAI,CAAC,qBAAqB,CACvB,KAAK,EACL,WAAW,EACX,MAAM,CACT;4BACG,CAAC,CAAC,MAAM;4BACR,CAAC,CAAC,EAAE,CAAA;gBAChB,CAAC;gBACD,WAAW,CAAC,QAAQ;oBAChB,mBAAmB,CAAC,MAAM,GAAG,CAAC;wBAC9B,CAAC,eAAe;wBAChB,CAAC,qBAAqB,CAAA;gBAC1B,WAAW,CAAC,UAAU;oBAClB,QAAQ,CAAC,aAAa,CAAC,KAAK,MAAM,CAAA;gBACtC,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAC5C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,gBAAgB,CAAC,KAAK,MAAM,CAC9C,CAAA;gBACD,WAAW,CAAC,WAAW;oBACnB,QAAQ,CAAC,iBAAiB,CAAC;wBAC3B,oBAAoB,CAAA;gBACxB,IAAI,WAAW,CAAC,WAAW;oBACvB,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAA;gBAEhD,IACI,QAAQ,CAAC,eAAe,CAAC,KAAK,IAAI;oBAClC,QAAQ,CAAC,eAAe,CAAC,KAAK,SAAS,EACzC,CAAC;oBACC,WAAW,CAAC,OAAO,GAAG,SAAS,CAAA;gBACnC,CAAC;qBAAM,CAAC;oBACJ,IACI,WAAW,CAAC,IAAI,KAAK,MAAM;wBAC3B,WAAW,CAAC,IAAI,KAAK,OAAO;wBAC5B,WAAW,CAAC,IAAI,KAAK,SAAS;wBAC9B,WAAW,CAAC,IAAI,KAAK,UAAU;wBAC/B,WAAW,CAAC,IAAI,KAAK,UAAU;wBAC/B,WAAW,CAAC,IAAI,KAAK,WAAW,EAClC,CAAC;wBACC,WAAW,CAAC,OAAO,GAAG,IAAI,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAA;oBAC1D,CAAC;yBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;wBACxC,WAAW,CAAC,OAAO;4BACf,QAAQ,CAAC,eAAe,CAAC,KAAK,GAAG;gCAC7B,CAAC,CAAC,MAAM;gCACR,CAAC,CAAC,OAAO,CAAA;oBACrB,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,OAAO;4BACf,QAAQ,CAAC,eAAe,CAAC,CAAA;oBACjC,CAAC;gBACL,CAAC;gBACD,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;oBACvB,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAA;gBAC9C,CAAC;gBACD,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACT,CAAA;YAED,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,mBAAQ,CAAC,IAAI,CACvC,aAAa,CAAC,MAAM,CAChB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,YAAY,CAAC;gBACtB,OAAO,CAAC,YAAY,CAAC;gBACzB,YAAY,CAAC,aAAa,CAAC;oBACvB,OAAO,CAAC,aAAa,CAAC;gBAC1B,YAAY,CAAC,iBAAiB,CAAC,KAAK,IAAI;gBACxC,YAAY,CAAC,iBAAiB,CAAC,KAAK,SAAS,CACpD,EACD,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpD,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAC/B,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,uBAAU,CAAC;oBAClB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBAChD,UAAU,EAAE,UAAU,CAAC,iBAAiB,CAAC;iBAC5C,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,kGAAkG;YAClG,MAAM,0BAA0B,GAAG,mBAAQ,CAAC,IAAI,CAC5C,aAAa,CAAC,MAAM,CAChB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,YAAY,CAAC;gBACtB,OAAO,CAAC,YAAY,CAAC;gBACzB,YAAY,CAAC,aAAa,CAAC;oBACvB,OAAO,CAAC,aAAa,CAAC,CACjC,EACD,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,YAAY,EAAE,EAAE;gBACb,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBAED,2GAA2G;gBAC3G,MAAM,MAAM,GAAG,gBAAgB,CAC3B,YAAY,EACZ,wBAAwB,CAC3B,CAAA;gBACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAClD,YAAY,CAAC,uBAAuB,CAAC,EACrC,MAAM,CACT,CAAA;gBAED,OAAO,IAAI,iCAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;oBACD,kBAAkB,EAAE,KAAK,CAAC,QAAQ;oBAClC,gBAAgB,EACZ,YAAY,CAAC,wBAAwB,CAAC;oBAC1C,mBAAmB,EAAE,mBAAmB;oBACxC,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,QAAQ,EACJ,YAAY,CAAC,aAAa,CAAC,KAAK,UAAU;wBACtC,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;oBACrC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,CAC1C,GAAG,EACH,GAAG,CACN;iBACJ,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,mBAAQ,CAAC,IAAI,CACvC,SAAS,CAAC,MAAM,CACZ,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC;gBAC/C,OAAO,CAAC,aAAa,CAAC,KAAK,OAAO,CAAC,aAAa,CAAC,CACxD,EACD,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CACrC,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACrD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvC,OAAO,CACH,KAAK,CAAC,aAAa,CAAC;wBAChB,UAAU,CAAC,aAAa,CAAC;wBAC7B,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC;wBAChD,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC,CACnD,CAAA;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,IAAI,uBAAU,CAAoB;oBACrC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC;oBAC9B,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,QAAQ,EACJ,UAAU,CAAC,YAAY,CAAC;wBACxB,UAAU,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACrD,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,KAAK,UAAU;iBACtD,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,iBAA2B;QAC9D,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;aAClD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE,CAAA;QAExE,6GAA6G;QAC7G,qEAAqE;QACrE,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACpD,OAAO,CACH,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC9B,CAAC,CAAC,KAAK,CAAC,QAAQ;oBAChB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAChD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,MAAM,uBAAuB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1D,OAAO,CACH,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC/B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CACjD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,kBAAkB,IAAI,CAAC,uBAAuB;gBAC/C,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,uBAAU,CAAC;oBACX,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;oBAC1B,QAAQ,EAAE,IAAI;iBACjB,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,yHAAyH;QACzH,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAClC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CACxC,CAAA;gBACD,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,uBAAU,CAAC;wBACX,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,WAAW,EAAE,MAAM,CAAC,WAAW;wBAC/B,QAAQ,EAAE,IAAI;qBACjB,CAAC,CACL,CAAA;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM;iBACzB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;oBACxB,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,KAAK,CAAC,UAAW,CACpB,CAAA;gBACP,OAAO,eAAe,SAAS,YAAY,KAAK,CAAC,UAAU,GAAG,CAAA;YAClE,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,KAAK,EACL,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBACL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,IAAI,UAAU,GAAG,eACb,EAAE,CAAC,IACP,kBAAkB,WAAW,gBAAgB,IAAI,CAAC,UAAU,CACxD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CACxB,KAAK,qBAAqB,GAAG,CAAA;gBAC9B,qDAAqD;gBACrD,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACd,MAAM,QAAQ,GACV,EAAE,CAAC,QAAQ,KAAK,WAAW;wBACvB,CAAC,CAAC,UAAU;wBACZ,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAA;oBACrB,UAAU,IAAI,cAAc,QAAQ,EAAE,CAAA;gBAC1C,CAAC;gBACD,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACd,MAAM,QAAQ,GACV,EAAE,CAAC,QAAQ,KAAK,WAAW;wBACvB,CAAC,CAAC,UAAU;wBACZ,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAA;oBACrB,UAAU,IAAI,cAAc,QAAQ,EAAE,CAAA;gBAC1C,CAAC;gBACD,IAAI,EAAE,CAAC,UAAU,EAAE,CAAC;oBAChB,UAAU,IAAI,IAAI,EAAE,CAAC,UAAU,EAAE,CAAA;gBACrC,CAAC;gBAED,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YACL,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,iBAAiB,cAAc,kBAAkB,WAAW,GAAG,CAAA;QAC1E,CAAC;QAED,GAAG,IAAI,GAAG,CAAA;QAEV,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,WAA2B,EAC3B,OAAiB;QAEjB,MAAM,KAAK,GAAG,OAAO;YACjB,CAAC,CAAC,wBAAwB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACxD,CAAC,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;QAClD,OAAO,IAAI,aAAK,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI;iBAC1C,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC3B,QAAQ,EAAE,EAAE,CACpB,CAAA;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAElE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1C,CAAC;QAED,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,qCAAiB,CAAC,IAAI;YAC5B,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,UAAyB;QAC3C,OAAO,IAAI,aAAK,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACnC,UAAyB;QAEzB,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;QAExE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1C,CAAC;QAED,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,qCAAiB,CAAC,IAAI;YAC5B,MAAM;YACN,IAAI;SACP,CAAC,CAAA;IACN,CAAC;IAES,YAAY,CAAC,KAAY,EAAE,MAAmB;QACpD,OAAO,eAAe,IAAI,CAAC,UAAU,CACjC,KAAK,CACR,SAAS,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAA;IAClD,CAAC;IAES,aAAa,CAAC,KAAY,EAAE,MAAmB;QACrD,OAAO,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,MAAM,CAAC,IAAI,IAAI,CAAA;IAC1E,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,SAAS,GAAG,EAAE,CAAA;QAClB,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACjB,SAAS,IAAI,SAAS,CAAA;QAC1B,CAAC;QACD,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,EAAE,CAAC;YAClE,SAAS,IAAI,WAAW,CAAA;QAC5B,CAAC;QAED,OAAO,IAAI,aAAK,CACZ,UAAU,SAAS,UAAU,KAAK,CAAC,IAAI,QAAQ,IAAI,CAAC,UAAU,CAC1D,KAAK,CACR,KAAK,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAChE,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,IAAI,aAAK,CAAC,eAAe,SAAS,GAAG,CAAC,CAAA;QACjD,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,aAAK,CACZ,eAAe,eAAe,CAAC,MAAM,MAAM,SAAS,GAAG,CAC1D,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CAAC,KAAY,EAAE,WAAqB;QAC7D,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAChE,KAAK,EACL,WAAW,CACd,CAAA;QACD,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,cAAc,kBAAkB,iBAAiB,GAAG,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,KAAY;QACpC,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAChE,KAAK,EACL,WAAW,CACd,CAAA;QACD,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,KAAY,EACZ,eAA2B;QAE3B,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,eAAe,CAAC,IACpB,YAAY,eAAe,CAAC,UAAU,GAAG,CAC5C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC5B,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,SAAS,GAAG,CACrC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;aACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;aACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,IAAI,GAAG,GACH,eAAe,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,oBACvC,UAAU,CAAC,IACf,kBAAkB,WAAW,IAAI;YACjC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;QAEjC,qDAAqD;QACrD,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,QAAQ,GACV,UAAU,CAAC,QAAQ,KAAK,WAAW;gBAC/B,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAA;YAC7B,GAAG,IAAI,cAAc,QAAQ,EAAE,CAAA;QACnC,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,QAAQ,GACV,UAAU,CAAC,QAAQ,KAAK,WAAW;gBAC/B,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAA;YAC7B,GAAG,IAAI,cAAc,QAAQ,EAAE,CAAA;QACnC,CAAC;QAED,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YACxB,GAAG,IAAI,IAAI,UAAU,CAAC,UAAU,EAAE,CAAA;QACtC,CAAC;QAED,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,cAAc,GAAG,iCAAe,CAAC,iBAAiB,CACpD,gBAAgB,CACnB;YACG,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACvB,CAAC,CAAC,gBAAgB,CAAA;QACtB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAErG,OAAO,IAAI,OAAO,GAAG,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAA6B;QAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAEhE,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,IAAI,MAAM,MAAM,SAAS,GAAG,CAAA;QACvC,CAAC;QAED,OAAO,IAAI,SAAS,GAAG,CAAA;IAC3B,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,MAAmB,EACnB,eAAyB,EACzB,gBAA0B;QAE1B,IAAI,CAAC,GACD,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACvE,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YAC1D,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,OAAO,CAAA;QACrC,CAAC;aAAM,IAAI,eAAe,EAAE,CAAC;YACzB,CAAC,IAAI,eAAe,CAAA;QACxB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACtB,2CAA2C;YAC3C,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI;gBAAE,CAAC,IAAI,WAAW,CAAA;iBAC3C,IAAI,gBAAgB;gBAAE,CAAC,IAAI,OAAO,CAAA;QAC3C,CAAC;QACD,IACI,MAAM,CAAC,WAAW,KAAK,IAAI;YAC3B,MAAM,CAAC,kBAAkB,KAAK,WAAW,EAC3C,CAAC;YACC,CAAC,IAAI,+BAA+B,CAAA;QACxC,CAAC;QACD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAA;QACzD,CAAC;QAED,OAAO,CAAC,CAAA;IACZ,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,oBAAY,CAClB,mDAAmD,CACtD,CAAA;IACL,CAAC;CACJ;AA5zGD,wCA4zGC", "file": "SapQueryRunner.js", "sourcesContent": ["import { promisify } from \"util\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { QueryFailedError, TypeORMError } from \"../../error\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionAlreadyStartedError } from \"../../error/TransactionAlreadyStartedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { QueryLock } from \"../../query-runner/QueryLock\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { TableIndexOptions } from \"../../schema-builder/options/TableIndexOptions\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { Query } from \"../Query\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { SapDriver } from \"./SapDriver\"\n\n/**\n * Runs queries on a single SQL Server database connection.\n */\nexport class SapQueryRunner extends BaseQueryRunner implements QueryRunner {\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: SapDriver\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Promise used to obtain a database connection from a pool for a first time.\n     */\n    protected databaseConnectionPromise: Promise<any>\n\n    private lock: QueryLock = new QueryLock()\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: SapDriver, mode: ReplicationMode) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n        this.mode = mode\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    async connect(): Promise<any> {\n        if (this.databaseConnection) return this.databaseConnection\n\n        this.databaseConnection = await this.driver.obtainMasterConnection()\n\n        return this.databaseConnection\n    }\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods once its released.\n     */\n    release(): Promise<void> {\n        this.isReleased = true\n\n        if (this.databaseConnection) {\n            return this.driver.master.release(this.databaseConnection)\n        }\n\n        return Promise.resolve()\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        if (\n            this.isTransactionActive &&\n            this.driver.transactionSupport === \"simple\"\n        )\n            throw new TransactionAlreadyStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n\n        this.isTransactionActive = true\n\n        /**\n         * Disable AUTOCOMMIT while running transaction.\n         *  Otherwise, COMMIT/ROLLBACK doesn't work in autocommit mode.\n         */\n        await this.setAutoCommit({ status: \"off\" })\n\n        if (isolationLevel) {\n            await this.query(\n                `SET TRANSACTION ISOLATION LEVEL ${isolationLevel || \"\"}`,\n            )\n        }\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        await this.query(\"COMMIT\")\n        this.isTransactionActive = false\n\n        await this.setAutoCommit({ status: \"on\" })\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        await this.query(\"ROLLBACK\")\n        this.isTransactionActive = false\n\n        await this.setAutoCommit({ status: \"on\" })\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * @description Switches on/off AUTOCOMMIT mode\n     * @link https://help.sap.com/docs/HANA_SERVICE_CF/7c78579ce9b14a669c1f3295b0d8ca16/d538d11053bd4f3f847ec5ce817a3d4c.html?locale=en-US\n     */\n    async setAutoCommit(options: { status: \"on\" | \"off\" }) {\n        const connection = await this.connect()\n\n        const execute = promisify(connection.exec.bind(connection))\n\n        connection.setAutoCommit(options.status === \"on\")\n\n        const query = `SET TRANSACTION AUTOCOMMIT DDL ${options.status.toUpperCase()};`\n        try {\n            await execute(query)\n        } catch (error) {\n            throw new QueryFailedError(query, [], error)\n        }\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const release = await this.lock.acquire()\n\n        const databaseConnection = await this.connect()\n\n        let statement: any\n        const result = new QueryResult()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n\n        try {\n            const queryStartTime = Date.now()\n            const isInsertQuery = query.substr(0, 11) === \"INSERT INTO\"\n\n            if (parameters?.some(Array.isArray)) {\n                statement = await promisify(databaseConnection.prepare).call(\n                    databaseConnection,\n                    query,\n                )\n            }\n\n            let raw: any\n            try {\n                raw = statement\n                    ? await promisify(statement.exec).call(\n                          statement,\n                          parameters,\n                      )\n                    : await promisify(databaseConnection.exec).call(\n                          databaseConnection,\n                          query,\n                          parameters,\n                          {},\n                      )\n            } catch (err) {\n                throw new QueryFailedError(query, parameters, err)\n            }\n\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.connection.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                raw,\n                undefined,\n            )\n\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            ) {\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n            }\n\n            if (typeof raw === \"number\") {\n                result.affected = raw\n            } else if (Array.isArray(raw)) {\n                result.records = raw\n            }\n\n            result.raw = raw\n\n            if (isInsertQuery) {\n                const lastIdQuery = `SELECT CURRENT_IDENTITY_VALUE() FROM \"SYS\".\"DUMMY\"`\n                this.driver.connection.logger.logQuery(lastIdQuery, [], this)\n                const identityValueResult = await new Promise<any>(\n                    (ok, fail) => {\n                        databaseConnection.exec(\n                            lastIdQuery,\n                            (err: any, raw: any) =>\n                                err\n                                    ? fail(\n                                          new QueryFailedError(\n                                              lastIdQuery,\n                                              [],\n                                              err,\n                                          ),\n                                      )\n                                    : ok(raw),\n                        )\n                    },\n                )\n\n                result.raw = identityValueResult[0][\"CURRENT_IDENTITY_VALUE()\"]\n                result.records = identityValueResult\n            }\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                undefined,\n                undefined,\n                err,\n            )\n            throw err\n        } finally {\n            // Never forget to drop the statement we reserved\n            if (statement?.drop) {\n                await new Promise<void>((ok) => statement.drop(() => ok()))\n            }\n\n            await broadcasterResult.wait()\n\n            // Always release the lock.\n            release()\n        }\n\n        if (useStructuredResult) {\n            return result\n        } else {\n            return result.raw\n        }\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    async stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const release = await this.lock.acquire()\n        let statement: any\n        let resultSet: any\n\n        const cleanup = async () => {\n            if (resultSet) {\n                await promisify(resultSet.close).call(resultSet)\n            }\n            if (statement) {\n                await promisify(statement.drop).call(statement)\n            }\n            release()\n        }\n\n        try {\n            const databaseConnection = await this.connect()\n            this.driver.connection.logger.logQuery(query, parameters, this)\n\n            statement = await promisify(databaseConnection.prepare).call(\n                databaseConnection,\n                query,\n            )\n            resultSet = await promisify(statement.executeQuery).call(\n                statement,\n                parameters,\n            )\n\n            const stream =\n                this.driver.streamClient.createObjectStream(resultSet)\n            stream.on(\"end\", async () => {\n                await cleanup()\n                onEnd?.()\n            })\n            stream.on(\"error\", async (error: Error) => {\n                this.driver.connection.logger.logQueryError(\n                    error,\n                    query,\n                    parameters,\n                    this,\n                )\n                await cleanup()\n                onError?.(error)\n            })\n\n            return stream\n        } catch (error) {\n            this.driver.connection.logger.logQueryError(\n                error,\n                query,\n                parameters,\n                this,\n            )\n            await cleanup()\n            throw new QueryFailedError(query, parameters, error)\n        }\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        const results: ObjectLiteral[] = await this.query(\n            `SELECT DATABASE_NAME FROM \"SYS\".\"M_DATABASES\"`,\n        )\n        return results.map((result) => result[\"DATABASE_NAME\"])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        const query = database\n            ? `SELECT * FROM \"${database}\".\"SYS\".\"SCHEMAS\"`\n            : `SELECT * FROM \"SYS\".\"SCHEMAS\"`\n        const results: ObjectLiteral[] = await this.query(query)\n        return results.map((result) => result[\"SCHEMA_NAME\"])\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        const databases = await this.getDatabases()\n        return databases.indexOf(database) !== -1\n    }\n\n    /**\n     * Returns current database.\n     */\n    async getCurrentDatabase(): Promise<string> {\n        const currentDBQuery: [{ dbName: string }] = await this.query(\n            `SELECT \"DATABASE_NAME\" AS \"dbName\" FROM \"SYS\".\"M_DATABASE\"`,\n        )\n\n        return currentDBQuery[0].dbName\n    }\n\n    /**\n     * Returns the database server version.\n     */\n    async getDatabaseAndVersion(): Promise<{\n        database: string\n        version: string\n    }> {\n        const currentDBQuery: [{ database: string; version: string }] =\n            await this.query(\n                `SELECT  \"DATABASE_NAME\" AS \"database\", \"VERSION\" AS \"version\" FROM \"SYS\".\"M_DATABASE\"`,\n            )\n\n        return currentDBQuery[0]\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        const schemas = await this.getSchemas()\n        return schemas.indexOf(schema) !== -1\n    }\n\n    /**\n     * Returns current schema.\n     */\n    async getCurrentSchema(): Promise<string> {\n        const currentSchemaQuery: [{ schemaName: string }] = await this.query(\n            `SELECT CURRENT_SCHEMA AS \"schemaName\" FROM \"SYS\".\"DUMMY\"`,\n        )\n\n        return currentSchemaQuery[0].schemaName\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT COUNT(*) as \"hasTable\" FROM \"SYS\".\"TABLES\" WHERE \"SCHEMA_NAME\" = '${parsedTableName.schema}' AND \"TABLE_NAME\" = '${parsedTableName.tableName}'`\n        const result: [{ hasTable: number }] = await this.query(sql)\n\n        return result[0].hasTable > 0\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT COUNT(*) as \"hasColumn\" FROM \"SYS\".\"TABLE_COLUMNS\" WHERE \"SCHEMA_NAME\" = '${parsedTableName.schema}' AND \"TABLE_NAME\" = '${parsedTableName.tableName}' AND \"COLUMN_NAME\" = '${columnName}'`\n        const result: [{ hasColumn: number }] = await this.query(sql)\n\n        return result[0].hasColumn > 0\n    }\n\n    /**\n     * Creates a new database.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Drops database.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        const schema =\n            schemaPath.indexOf(\".\") === -1\n                ? schemaPath\n                : schemaPath.split(\".\")[1]\n\n        let exist = false\n        if (ifNotExist) {\n            const result = await this.query(\n                `SELECT * FROM \"SYS\".\"SCHEMAS\" WHERE \"SCHEMA_NAME\" = '${schema}'`,\n            )\n            exist = !!result.length\n        }\n        if (!ifNotExist || (ifNotExist && !exist)) {\n            const up = `CREATE SCHEMA \"${schema}\"`\n            const down = `DROP SCHEMA \"${schema}\" CASCADE`\n            await this.executeQueries(new Query(up), new Query(down))\n        }\n    }\n\n    /**\n     * Drops table schema\n     */\n    async dropSchema(\n        schemaPath: string,\n        ifExist?: boolean,\n        isCascade?: boolean,\n    ): Promise<void> {\n        const schema =\n            schemaPath.indexOf(\".\") === -1\n                ? schemaPath\n                : schemaPath.split(\".\")[0]\n        let exist = false\n        if (ifExist) {\n            const result = await this.query(\n                `SELECT * FROM \"SYS\".\"SCHEMAS\" WHERE \"SCHEMA_NAME\" = '${schema}'`,\n            )\n            exist = !!result.length\n        }\n        if (!ifExist || (ifExist && exist)) {\n            const up = `DROP SCHEMA \"${schema}\" ${isCascade ? \"CASCADE\" : \"\"}`\n            const down = `CREATE SCHEMA \"${schema}\"`\n            await this.executeQueries(new Query(up), new Query(down))\n        }\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n        createIndices: boolean = true,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        // if createForeignKeys is true, we must drop created foreign keys in down query.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (createForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                downQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        if (createIndices) {\n            table.indices.forEach((index) => {\n                // new index may be passed without name. In this case we generate index name manually.\n                if (!index.name)\n                    index.name = this.connection.namingStrategy.indexName(\n                        table,\n                        index.columnNames,\n                        index.where,\n                    )\n                upQueries.push(this.createIndexSql(table, index))\n                downQueries.push(this.dropIndexSql(table, index))\n            })\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n        dropIndices: boolean = true,\n    ): Promise<void> {\n        if (ifExist) {\n            const isTableExist = await this.hasTable(tableOrName)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // It needs because if table does not exist and dropForeignKeys or dropIndices is true, we don't need\n        // to perform drop queries for foreign keys and indices.\n\n        if (dropIndices) {\n            table.indices.forEach((index) => {\n                upQueries.push(this.dropIndexSql(table, index))\n                downQueries.push(this.createIndexSql(table, index))\n            })\n        }\n\n        // if dropForeignKeys is true, we just drop the table, otherwise we also drop table foreign keys.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (dropForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                upQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        upQueries.push(this.dropTableSql(table))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(\n        view: View,\n        syncWithMetadata: boolean = false,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        if (syncWithMetadata)\n            upQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        if (syncWithMetadata)\n            downQueries.push(await this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = InstanceChecker.isView(target) ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(await this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames a table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        const oldTable = InstanceChecker.isTable(oldTableOrName)\n            ? oldTableOrName\n            : await this.getCachedTable(oldTableOrName)\n        const newTable = oldTable.clone()\n\n        const { schema: schemaName, tableName: oldTableName } =\n            this.driver.parseTableName(oldTable)\n\n        newTable.name = schemaName\n            ? `${schemaName}.${newTableName}`\n            : newTableName\n\n        // rename table\n        upQueries.push(\n            new Query(\n                `RENAME TABLE ${this.escapePath(oldTable)} TO ${this.escapePath(\n                    newTable,\n                )}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `RENAME TABLE ${this.escapePath(newTable)} TO ${this.escapePath(\n                    oldTable,\n                )}`,\n            ),\n        )\n\n        // drop old FK's. Foreign keys must be dropped before the primary keys are dropped\n        newTable.foreignKeys.forEach((foreignKey) => {\n            upQueries.push(this.dropForeignKeySql(newTable, foreignKey))\n            downQueries.push(this.createForeignKeySql(newTable, foreignKey))\n        })\n\n        // SAP HANA does not allow to drop PK's which is referenced by foreign keys.\n        // To avoid this, we must drop all referential foreign keys and recreate them later\n        const referencedForeignKeySql = `SELECT * FROM \"SYS\".\"REFERENTIAL_CONSTRAINTS\" WHERE \"REFERENCED_SCHEMA_NAME\" = '${schemaName}' AND \"REFERENCED_TABLE_NAME\" = '${oldTableName}'`\n        const dbForeignKeys: ObjectLiteral[] = await this.query(\n            referencedForeignKeySql,\n        )\n        let referencedForeignKeys: TableForeignKey[] = []\n        const referencedForeignKeyTableMapping: {\n            tableName: string\n            fkName: string\n        }[] = []\n        if (dbForeignKeys.length > 0) {\n            referencedForeignKeys = dbForeignKeys.map((dbForeignKey) => {\n                const foreignKeys = dbForeignKeys.filter(\n                    (dbFk) =>\n                        dbFk[\"CONSTRAINT_NAME\"] ===\n                        dbForeignKey[\"CONSTRAINT_NAME\"],\n                )\n\n                referencedForeignKeyTableMapping.push({\n                    tableName: `${dbForeignKey[\"SCHEMA_NAME\"]}.${dbForeignKey[\"TABLE_NAME\"]}`,\n                    fkName: dbForeignKey[\"CONSTRAINT_NAME\"],\n                })\n                return new TableForeignKey({\n                    name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                    columnNames: foreignKeys.map((dbFk) => dbFk[\"COLUMN_NAME\"]),\n                    referencedDatabase: newTable.database,\n                    referencedSchema: newTable.schema,\n                    referencedTableName: newTable.name, // we use renamed table name\n                    referencedColumnNames: foreignKeys.map(\n                        (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                    ),\n                    onDelete:\n                        dbForeignKey[\"DELETE_RULE\"] === \"RESTRICT\"\n                            ? \"NO ACTION\"\n                            : dbForeignKey[\"DELETE_RULE\"],\n                    onUpdate:\n                        dbForeignKey[\"UPDATE_RULE\"] === \"RESTRICT\"\n                            ? \"NO ACTION\"\n                            : dbForeignKey[\"UPDATE_RULE\"],\n                    deferrable: dbForeignKey[\"CHECK_TIME\"].replace(\"_\", \" \"), // \"CHECK_TIME\" is \"INITIALLY_IMMEDIATE\" or \"INITIALLY DEFERRED\"\n                })\n            })\n\n            // drop referenced foreign keys\n            referencedForeignKeys.forEach((foreignKey) => {\n                const mapping = referencedForeignKeyTableMapping.find(\n                    (it) => it.fkName === foreignKey.name,\n                )\n                upQueries.push(\n                    this.dropForeignKeySql(mapping!.tableName, foreignKey),\n                )\n                downQueries.push(\n                    this.createForeignKeySql(mapping!.tableName, foreignKey),\n                )\n            })\n        }\n\n        // rename primary key constraint\n        if (newTable.primaryColumns.length > 0) {\n            const columnNames = newTable.primaryColumns.map(\n                (column) => column.name,\n            )\n            const columnNamesString = columnNames\n                .map((columnName) => `\"${columnName}\"`)\n                .join(\", \")\n\n            const oldPkName = this.connection.namingStrategy.primaryKeyName(\n                oldTable,\n                columnNames,\n            )\n            const newPkName = this.connection.namingStrategy.primaryKeyName(\n                newTable,\n                columnNames,\n            )\n\n            // drop old PK\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} DROP CONSTRAINT \"${oldPkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} ADD CONSTRAINT \"${oldPkName}\" PRIMARY KEY (${columnNamesString})`,\n                ),\n            )\n\n            // create new PK\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} ADD CONSTRAINT \"${newPkName}\" PRIMARY KEY (${columnNamesString})`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} DROP CONSTRAINT \"${newPkName}\"`,\n                ),\n            )\n        }\n\n        // recreate foreign keys with new constraint names\n        newTable.foreignKeys.forEach((foreignKey) => {\n            // replace constraint name\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                newTable,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n            // create new FK's\n            upQueries.push(this.createForeignKeySql(newTable, foreignKey))\n            downQueries.push(this.dropForeignKeySql(newTable, foreignKey))\n        })\n\n        // restore referenced foreign keys\n        referencedForeignKeys.forEach((foreignKey) => {\n            const mapping = referencedForeignKeyTableMapping.find(\n                (it) => it.fkName === foreignKey.name,\n            )\n            upQueries.push(\n                this.createForeignKeySql(mapping!.tableName, foreignKey),\n            )\n            downQueries.push(\n                this.dropForeignKeySql(mapping!.tableName, foreignKey),\n            )\n        })\n\n        // rename index constraints\n        newTable.indices.forEach((index) => {\n            // build new constraint name\n            const newIndexName = this.connection.namingStrategy.indexName(\n                newTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // drop old index\n            upQueries.push(this.dropIndexSql(newTable, index))\n            downQueries.push(this.createIndexSql(newTable, index))\n\n            // replace constraint name\n            index.name = newIndexName\n\n            // create new index\n            upQueries.push(this.createIndexSql(newTable, index))\n            downQueries.push(this.dropIndexSql(newTable, index))\n        })\n\n        await this.executeQueries(upQueries, downQueries)\n\n        // rename old table and replace it in cached tabled;\n        oldTable.name = newTable.name\n        this.replaceCachedTable(oldTable, newTable)\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const parsedTableName = this.driver.parseTableName(table)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(new Query(this.addColumnSql(table, column)))\n        downQueries.push(new Query(this.dropColumnSql(table, column)))\n\n        // create or update primary key constraint\n        if (column.isPrimary) {\n            const primaryColumns = clonedTable.primaryColumns\n            // if table already have primary key, me must drop it and recreate again\n            if (primaryColumns.length > 0) {\n                // SAP HANA does not allow to drop PK's which is referenced by foreign keys.\n                // To avoid this, we must drop all referential foreign keys and recreate them later\n                const referencedForeignKeySql = `SELECT * FROM \"SYS\".\"REFERENTIAL_CONSTRAINTS\" WHERE \"REFERENCED_SCHEMA_NAME\" = '${parsedTableName.schema}' AND \"REFERENCED_TABLE_NAME\" = '${parsedTableName.tableName}'`\n                const dbForeignKeys: ObjectLiteral[] = await this.query(\n                    referencedForeignKeySql,\n                )\n                let referencedForeignKeys: TableForeignKey[] = []\n                const referencedForeignKeyTableMapping: {\n                    tableName: string\n                    fkName: string\n                }[] = []\n                if (dbForeignKeys.length > 0) {\n                    referencedForeignKeys = dbForeignKeys.map(\n                        (dbForeignKey) => {\n                            const foreignKeys = dbForeignKeys.filter(\n                                (dbFk) =>\n                                    dbFk[\"CONSTRAINT_NAME\"] ===\n                                    dbForeignKey[\"CONSTRAINT_NAME\"],\n                            )\n\n                            referencedForeignKeyTableMapping.push({\n                                tableName: `${dbForeignKey[\"SCHEMA_NAME\"]}.${dbForeignKey[\"TABLE_NAME\"]}`,\n                                fkName: dbForeignKey[\"CONSTRAINT_NAME\"],\n                            })\n                            return new TableForeignKey({\n                                name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                                columnNames: foreignKeys.map(\n                                    (dbFk) => dbFk[\"COLUMN_NAME\"],\n                                ),\n                                referencedDatabase: table.database,\n                                referencedSchema: table.schema,\n                                referencedTableName: table.name,\n                                referencedColumnNames: foreignKeys.map(\n                                    (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                                ),\n                                onDelete:\n                                    dbForeignKey[\"DELETE_RULE\"] === \"RESTRICT\"\n                                        ? \"NO ACTION\"\n                                        : dbForeignKey[\"DELETE_RULE\"],\n                                onUpdate:\n                                    dbForeignKey[\"UPDATE_RULE\"] === \"RESTRICT\"\n                                        ? \"NO ACTION\"\n                                        : dbForeignKey[\"UPDATE_RULE\"],\n                                deferrable: dbForeignKey[\"CHECK_TIME\"].replace(\n                                    \"_\",\n                                    \" \",\n                                ),\n                            })\n                        },\n                    )\n\n                    // drop referenced foreign keys\n                    referencedForeignKeys.forEach((foreignKey) => {\n                        const mapping = referencedForeignKeyTableMapping.find(\n                            (it) => it.fkName === foreignKey.name,\n                        )\n                        upQueries.push(\n                            this.dropForeignKeySql(\n                                mapping!.tableName,\n                                foreignKey,\n                            ),\n                        )\n                        downQueries.push(\n                            this.createForeignKeySql(\n                                mapping!.tableName,\n                                foreignKey,\n                            ),\n                        )\n                    })\n                }\n\n                const pkName = this.connection.namingStrategy.primaryKeyName(\n                    clonedTable,\n                    primaryColumns.map((column) => column.name),\n                )\n                const columnNames = primaryColumns\n                    .map((column) => `\"${column.name}\"`)\n                    .join(\", \")\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n\n                // restore referenced foreign keys\n                referencedForeignKeys.forEach((foreignKey) => {\n                    const mapping = referencedForeignKeyTableMapping.find(\n                        (it) => it.fkName === foreignKey.name,\n                    )\n                    upQueries.push(\n                        this.createForeignKeySql(\n                            mapping!.tableName,\n                            foreignKey,\n                        ),\n                    )\n                    downQueries.push(\n                        this.dropForeignKeySql(mapping!.tableName, foreignKey),\n                    )\n                })\n            }\n\n            primaryColumns.push(column)\n            const pkName = this.connection.namingStrategy.primaryKeyName(\n                clonedTable,\n                primaryColumns.map((column) => column.name),\n            )\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n        }\n\n        // create column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            upQueries.push(this.createIndexSql(table, columnIndex))\n            downQueries.push(this.dropIndexSql(table, columnIndex))\n        } else if (column.isUnique) {\n            const uniqueIndex = new TableIndex({\n                name: this.connection.namingStrategy.indexName(table, [\n                    column.name,\n                ]),\n                columnNames: [column.name],\n                isUnique: true,\n            })\n            clonedTable.indices.push(uniqueIndex)\n            clonedTable.uniques.push(\n                new TableUnique({\n                    name: uniqueIndex.name,\n                    columnNames: uniqueIndex.columnNames,\n                }),\n            )\n            upQueries.push(this.createIndexSql(table, uniqueIndex))\n            downQueries.push(this.dropIndexSql(table, uniqueIndex))\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.addColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.addColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        let newColumn: TableColumn | undefined = undefined\n        if (InstanceChecker.isTableColumn(newTableColumnOrName)) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        await this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        let clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find(\n                  (column) => column.name === oldTableColumnOrName,\n              )\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        if (\n            (newColumn.isGenerated !== oldColumn.isGenerated &&\n                newColumn.generationStrategy !== \"uuid\") ||\n            newColumn.type !== oldColumn.type ||\n            newColumn.length !== oldColumn.length\n        ) {\n            // SQL Server does not support changing of IDENTITY column, so we must drop column and recreate it again.\n            // Also, we recreate column if column type changed\n            await this.dropColumn(table, oldColumn)\n            await this.addColumn(table, newColumn)\n\n            // update cloned table\n            clonedTable = table.clone()\n        } else {\n            if (newColumn.name !== oldColumn.name) {\n                // rename column\n                upQueries.push(\n                    new Query(\n                        `RENAME COLUMN ${this.escapePath(table)}.\"${\n                            oldColumn.name\n                        }\" TO \"${newColumn.name}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `RENAME COLUMN ${this.escapePath(table)}.\"${\n                            newColumn.name\n                        }\" TO \"${oldColumn.name}\"`,\n                    ),\n                )\n\n                if (oldColumn.isPrimary === true) {\n                    const primaryColumns = clonedTable.primaryColumns\n\n                    // build old primary constraint name\n                    const columnNames = primaryColumns.map(\n                        (column) => column.name,\n                    )\n                    const oldPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    // replace old column name with new column name\n                    columnNames.splice(columnNames.indexOf(oldColumn.name), 1)\n                    columnNames.push(newColumn.name)\n                    const columnNamesString = columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n\n                    // drop old PK\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                clonedTable,\n                            )} DROP CONSTRAINT \"${oldPkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                clonedTable,\n                            )} ADD CONSTRAINT \"${oldPkName}\" PRIMARY KEY (${columnNamesString})`,\n                        ),\n                    )\n\n                    // build new primary constraint name\n                    const newPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    // create new PK\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                clonedTable,\n                            )} ADD CONSTRAINT \"${newPkName}\" PRIMARY KEY (${columnNamesString})`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                clonedTable,\n                            )} DROP CONSTRAINT \"${newPkName}\"`,\n                        ),\n                    )\n                }\n\n                // rename index constraints\n                clonedTable.findColumnIndices(oldColumn).forEach((index) => {\n                    // build new constraint name\n                    index.columnNames.splice(\n                        index.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    index.columnNames.push(newColumn.name)\n                    const newIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // drop old index\n                    upQueries.push(this.dropIndexSql(clonedTable, index))\n                    downQueries.push(this.createIndexSql(clonedTable, index))\n\n                    // replace constraint name\n                    index.name = newIndexName\n\n                    // create new index\n                    upQueries.push(this.createIndexSql(clonedTable, index))\n                    downQueries.push(this.dropIndexSql(clonedTable, index))\n                })\n\n                // rename foreign key constraints\n                clonedTable\n                    .findColumnForeignKeys(oldColumn)\n                    .forEach((foreignKey) => {\n                        // build new constraint name\n                        foreignKey.columnNames.splice(\n                            foreignKey.columnNames.indexOf(oldColumn.name),\n                            1,\n                        )\n                        foreignKey.columnNames.push(newColumn.name)\n                        const newForeignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        upQueries.push(\n                            this.dropForeignKeySql(clonedTable, foreignKey),\n                        )\n                        downQueries.push(\n                            this.createForeignKeySql(clonedTable, foreignKey),\n                        )\n\n                        // replace constraint name\n                        foreignKey.name = newForeignKeyName\n\n                        // create new FK's\n                        upQueries.push(\n                            this.createForeignKeySql(clonedTable, foreignKey),\n                        )\n                        downQueries.push(\n                            this.dropForeignKeySql(clonedTable, foreignKey),\n                        )\n                    })\n\n                // rename check constraints\n                clonedTable.findColumnChecks(oldColumn).forEach((check) => {\n                    // build new constraint name\n                    check.columnNames!.splice(\n                        check.columnNames!.indexOf(oldColumn.name),\n                        1,\n                    )\n                    check.columnNames!.push(newColumn.name)\n                    const newCheckName =\n                        this.connection.namingStrategy.checkConstraintName(\n                            clonedTable,\n                            check.expression!,\n                        )\n\n                    upQueries.push(\n                        this.dropCheckConstraintSql(clonedTable, check),\n                    )\n                    downQueries.push(\n                        this.createCheckConstraintSql(clonedTable, check),\n                    )\n\n                    // replace constraint name\n                    check.name = newCheckName\n\n                    upQueries.push(\n                        this.createCheckConstraintSql(clonedTable, check),\n                    )\n                    downQueries.push(\n                        this.dropCheckConstraintSql(clonedTable, check),\n                    )\n                })\n\n                // rename old column in the Table object\n                const oldTableColumn = clonedTable.columns.find(\n                    (column) => column.name === oldColumn.name,\n                )\n                clonedTable.columns[\n                    clonedTable.columns.indexOf(oldTableColumn!)\n                ].name = newColumn.name\n                oldColumn.name = newColumn.name\n            }\n\n            if (this.isColumnChanged(oldColumn, newColumn, true)) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ALTER (${this.buildCreateColumnSql(\n                            newColumn,\n                            !(\n                                oldColumn.default === null ||\n                                oldColumn.default === undefined\n                            ),\n                            !oldColumn.isNullable,\n                        )})`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ALTER (${this.buildCreateColumnSql(\n                            oldColumn,\n                            !(\n                                newColumn.default === null ||\n                                newColumn.default === undefined\n                            ),\n                            !newColumn.isNullable,\n                        )})`,\n                    ),\n                )\n            } else if (oldColumn.comment !== newColumn.comment) {\n                upQueries.push(\n                    new Query(\n                        `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                            oldColumn.name\n                        }\" IS ${this.escapeComment(newColumn.comment)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                            newColumn.name\n                        }\" IS ${this.escapeComment(oldColumn.comment)}`,\n                    ),\n                )\n            }\n\n            if (newColumn.isPrimary !== oldColumn.isPrimary) {\n                const primaryColumns = clonedTable.primaryColumns\n\n                // if primary column state changed, we must always drop existed constraint.\n                if (primaryColumns.length > 0) {\n                    const pkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            primaryColumns.map((column) => column.name),\n                        )\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                }\n\n                if (newColumn.isPrimary === true) {\n                    primaryColumns.push(newColumn)\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = true\n                    const pkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            primaryColumns.map((column) => column.name),\n                        )\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                } else {\n                    const primaryColumn = primaryColumns.find(\n                        (c) => c.name === newColumn.name,\n                    )\n                    primaryColumns.splice(\n                        primaryColumns.indexOf(primaryColumn!),\n                        1,\n                    )\n\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = false\n\n                    // if we have another primary keys, we must recreate constraint.\n                    if (primaryColumns.length > 0) {\n                        const pkName =\n                            this.connection.namingStrategy.primaryKeyName(\n                                clonedTable,\n                                primaryColumns.map((column) => column.name),\n                            )\n                        const columnNames = primaryColumns\n                            .map((column) => `\"${column.name}\"`)\n                            .join(\", \")\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} DROP CONSTRAINT \"${pkName}\"`,\n                            ),\n                        )\n                    }\n                }\n            }\n\n            if (newColumn.isUnique !== oldColumn.isUnique) {\n                if (newColumn.isUnique === true) {\n                    const uniqueIndex = new TableIndex({\n                        name: this.connection.namingStrategy.indexName(table, [\n                            newColumn.name,\n                        ]),\n                        columnNames: [newColumn.name],\n                        isUnique: true,\n                    })\n                    clonedTable.indices.push(uniqueIndex)\n                    clonedTable.uniques.push(\n                        new TableUnique({\n                            name: uniqueIndex.name,\n                            columnNames: uniqueIndex.columnNames,\n                        }),\n                    )\n                    upQueries.push(this.createIndexSql(table, uniqueIndex))\n                    downQueries.push(this.dropIndexSql(table, uniqueIndex))\n                } else {\n                    const uniqueIndex = clonedTable.indices.find((index) => {\n                        return (\n                            index.columnNames.length === 1 &&\n                            index.isUnique === true &&\n                            !!index.columnNames.find(\n                                (columnName) => columnName === newColumn.name,\n                            )\n                        )\n                    })\n                    clonedTable.indices.splice(\n                        clonedTable.indices.indexOf(uniqueIndex!),\n                        1,\n                    )\n\n                    const tableUnique = clonedTable.uniques.find(\n                        (unique) => unique.name === uniqueIndex!.name,\n                    )\n                    clonedTable.uniques.splice(\n                        clonedTable.uniques.indexOf(tableUnique!),\n                        1,\n                    )\n\n                    upQueries.push(this.dropIndexSql(table, uniqueIndex!))\n                    downQueries.push(this.createIndexSql(table, uniqueIndex!))\n                }\n            }\n\n            await this.executeQueries(upQueries, downQueries)\n            this.replaceCachedTable(table, clonedTable)\n        }\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        for (const { oldColumn, newColumn } of changedColumns) {\n            await this.changeColumn(tableOrName, oldColumn, newColumn)\n        }\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const parsedTableName = this.driver.parseTableName(table)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const column = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName\n            : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table \"${table.name}\"`,\n            )\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop primary key constraint\n        if (column.isPrimary) {\n            // SAP HANA does not allow to drop PK's which is referenced by foreign keys.\n            // To avoid this, we must drop all referential foreign keys and recreate them later\n            const referencedForeignKeySql = `SELECT * FROM \"SYS\".\"REFERENTIAL_CONSTRAINTS\" WHERE \"REFERENCED_SCHEMA_NAME\" = '${parsedTableName.schema}' AND \"REFERENCED_TABLE_NAME\" = '${parsedTableName.tableName}'`\n            const dbForeignKeys: ObjectLiteral[] = await this.query(\n                referencedForeignKeySql,\n            )\n            let referencedForeignKeys: TableForeignKey[] = []\n            const referencedForeignKeyTableMapping: {\n                tableName: string\n                fkName: string\n            }[] = []\n            if (dbForeignKeys.length > 0) {\n                referencedForeignKeys = dbForeignKeys.map((dbForeignKey) => {\n                    const foreignKeys = dbForeignKeys.filter(\n                        (dbFk) =>\n                            dbFk[\"CONSTRAINT_NAME\"] ===\n                            dbForeignKey[\"CONSTRAINT_NAME\"],\n                    )\n\n                    referencedForeignKeyTableMapping.push({\n                        tableName: `${dbForeignKey[\"SCHEMA_NAME\"]}.${dbForeignKey[\"TABLE_NAME\"]}`,\n                        fkName: dbForeignKey[\"CONSTRAINT_NAME\"],\n                    })\n                    return new TableForeignKey({\n                        name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                        columnNames: foreignKeys.map(\n                            (dbFk) => dbFk[\"COLUMN_NAME\"],\n                        ),\n                        referencedDatabase: table.database,\n                        referencedSchema: table.schema,\n                        referencedTableName: table.name,\n                        referencedColumnNames: foreignKeys.map(\n                            (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                        ),\n                        onDelete:\n                            dbForeignKey[\"DELETE_RULE\"] === \"RESTRICT\"\n                                ? \"NO ACTION\"\n                                : dbForeignKey[\"DELETE_RULE\"],\n                        onUpdate:\n                            dbForeignKey[\"UPDATE_RULE\"] === \"RESTRICT\"\n                                ? \"NO ACTION\"\n                                : dbForeignKey[\"UPDATE_RULE\"],\n                        deferrable: dbForeignKey[\"CHECK_TIME\"].replace(\n                            \"_\",\n                            \" \",\n                        ),\n                    })\n                })\n\n                // drop referenced foreign keys\n                referencedForeignKeys.forEach((foreignKey) => {\n                    const mapping = referencedForeignKeyTableMapping.find(\n                        (it) => it.fkName === foreignKey.name,\n                    )\n                    upQueries.push(\n                        this.dropForeignKeySql(mapping!.tableName, foreignKey),\n                    )\n                    downQueries.push(\n                        this.createForeignKeySql(\n                            mapping!.tableName,\n                            foreignKey,\n                        ),\n                    )\n                })\n            }\n\n            const pkName = this.connection.namingStrategy.primaryKeyName(\n                clonedTable,\n                clonedTable.primaryColumns.map((column) => column.name),\n            )\n            const columnNames = clonedTable.primaryColumns\n                .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n\n            // update column in table\n            const tableColumn = clonedTable.findColumnByName(column.name)\n            tableColumn!.isPrimary = false\n\n            // if primary key have multiple columns, we must recreate it without dropped column\n            if (clonedTable.primaryColumns.length > 0) {\n                const pkName = this.connection.namingStrategy.primaryKeyName(\n                    clonedTable,\n                    clonedTable.primaryColumns.map((column) => column.name),\n                )\n                const columnNames = clonedTable.primaryColumns\n                    .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                    .join(\", \")\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n            }\n\n            // restore referenced foreign keys\n            referencedForeignKeys.forEach((foreignKey) => {\n                const mapping = referencedForeignKeyTableMapping.find(\n                    (it) => it.fkName === foreignKey.name,\n                )\n                upQueries.push(\n                    this.createForeignKeySql(mapping!.tableName, foreignKey),\n                )\n                downQueries.push(\n                    this.dropForeignKeySql(mapping!.tableName, foreignKey),\n                )\n            })\n        }\n\n        // drop column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            clonedTable.indices.splice(\n                clonedTable.indices.indexOf(columnIndex),\n                1,\n            )\n            upQueries.push(this.dropIndexSql(table, columnIndex))\n            downQueries.push(this.createIndexSql(table, columnIndex))\n        } else if (column.isUnique) {\n            // we splice constraints both from table uniques and indices.\n            const uniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(table, [\n                    column.name,\n                ])\n            const foundUnique = clonedTable.uniques.find(\n                (unique) => unique.name === uniqueName,\n            )\n            if (foundUnique) {\n                clonedTable.uniques.splice(\n                    clonedTable.uniques.indexOf(foundUnique),\n                    1,\n                )\n                upQueries.push(this.dropIndexSql(table, uniqueName))\n                downQueries.push(\n                    new Query(\n                        `CREATE UNIQUE INDEX \"${uniqueName}\" ON ${this.escapePath(\n                            table,\n                        )} (\"${column.name}\")`,\n                    ),\n                )\n            }\n\n            const indexName = this.connection.namingStrategy.indexName(table, [\n                column.name,\n            ])\n            const foundIndex = clonedTable.indices.find(\n                (index) => index.name === indexName,\n            )\n            if (foundIndex) {\n                clonedTable.indices.splice(\n                    clonedTable.indices.indexOf(foundIndex),\n                    1,\n                )\n                upQueries.push(this.dropIndexSql(table, indexName))\n                downQueries.push(\n                    new Query(\n                        `CREATE UNIQUE INDEX \"${indexName}\" ON ${this.escapePath(\n                            table,\n                        )} (\"${column.name}\")`,\n                    ),\n                )\n            }\n        }\n\n        // drop column check\n        const columnCheck = clonedTable.checks.find(\n            (check) =>\n                !!check.columnNames &&\n                check.columnNames.length === 1 &&\n                check.columnNames[0] === column.name,\n        )\n        if (columnCheck) {\n            clonedTable.checks.splice(\n                clonedTable.checks.indexOf(columnCheck),\n                1,\n            )\n            upQueries.push(this.dropCheckConstraintSql(table, columnCheck))\n            downQueries.push(this.createCheckConstraintSql(table, columnCheck))\n        }\n\n        upQueries.push(new Query(this.dropColumnSql(table, column)))\n        downQueries.push(new Query(this.addColumnSql(table, column)))\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.removeColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.dropColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n\n        const up = this.createPrimaryKeySql(table, columnNames)\n\n        // mark columns as primary, because dropPrimaryKeySql build constraint name from table primary column names.\n        clonedTable.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n        const down = this.dropPrimaryKeySql(clonedTable)\n\n        await this.executeQueries(up, down)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const parsedTableName = this.driver.parseTableName(table)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const clonedTable = table.clone()\n        const columnNames = columns.map((column) => column.name)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // SAP HANA does not allow to drop PK's which is referenced by foreign keys.\n        // To avoid this, we must drop all referential foreign keys and recreate them later\n        const referencedForeignKeySql = `SELECT * FROM \"SYS\".\"REFERENTIAL_CONSTRAINTS\" WHERE \"REFERENCED_SCHEMA_NAME\" = '${parsedTableName.schema}' AND \"REFERENCED_TABLE_NAME\" = '${parsedTableName.tableName}'`\n        const dbForeignKeys: ObjectLiteral[] = await this.query(\n            referencedForeignKeySql,\n        )\n        let referencedForeignKeys: TableForeignKey[] = []\n        const referencedForeignKeyTableMapping: {\n            tableName: string\n            fkName: string\n        }[] = []\n        if (dbForeignKeys.length > 0) {\n            referencedForeignKeys = dbForeignKeys.map((dbForeignKey) => {\n                const foreignKeys = dbForeignKeys.filter(\n                    (dbFk) =>\n                        dbFk[\"CONSTRAINT_NAME\"] ===\n                        dbForeignKey[\"CONSTRAINT_NAME\"],\n                )\n\n                referencedForeignKeyTableMapping.push({\n                    tableName: `${dbForeignKey[\"SCHEMA_NAME\"]}.${dbForeignKey[\"TABLE_NAME\"]}`,\n                    fkName: dbForeignKey[\"CONSTRAINT_NAME\"],\n                })\n                return new TableForeignKey({\n                    name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                    columnNames: foreignKeys.map((dbFk) => dbFk[\"COLUMN_NAME\"]),\n                    referencedDatabase: table.database,\n                    referencedSchema: table.schema,\n                    referencedTableName: table.name,\n                    referencedColumnNames: foreignKeys.map(\n                        (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                    ),\n                    onDelete:\n                        dbForeignKey[\"DELETE_RULE\"] === \"RESTRICT\"\n                            ? \"NO ACTION\"\n                            : dbForeignKey[\"DELETE_RULE\"],\n                    onUpdate:\n                        dbForeignKey[\"UPDATE_RULE\"] === \"RESTRICT\"\n                            ? \"NO ACTION\"\n                            : dbForeignKey[\"UPDATE_RULE\"],\n                    deferrable: dbForeignKey[\"CHECK_TIME\"].replace(\"_\", \" \"),\n                })\n            })\n\n            // drop referenced foreign keys\n            referencedForeignKeys.forEach((foreignKey) => {\n                const mapping = referencedForeignKeyTableMapping.find(\n                    (it) => it.fkName === foreignKey.name,\n                )\n                upQueries.push(\n                    this.dropForeignKeySql(mapping!.tableName, foreignKey),\n                )\n                downQueries.push(\n                    this.createForeignKeySql(mapping!.tableName, foreignKey),\n                )\n            })\n        }\n\n        // if table already have primary columns, we must drop them.\n        const primaryColumns = clonedTable.primaryColumns\n        if (primaryColumns.length > 0) {\n            const pkName = this.connection.namingStrategy.primaryKeyName(\n                clonedTable,\n                primaryColumns.map((column) => column.name),\n            )\n            const columnNamesString = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n                ),\n            )\n        }\n\n        // update columns in table.\n        clonedTable.columns\n            .filter((column) => columnNames.indexOf(column.name) !== -1)\n            .forEach((column) => (column.isPrimary = true))\n\n        const pkName = this.connection.namingStrategy.primaryKeyName(\n            clonedTable,\n            columnNames,\n        )\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} DROP CONSTRAINT \"${pkName}\"`,\n            ),\n        )\n\n        // restore referenced foreign keys\n        referencedForeignKeys.forEach((foreignKey) => {\n            const mapping = referencedForeignKeyTableMapping.find(\n                (it) => it.fkName === foreignKey.name,\n            )\n            upQueries.push(\n                this.createForeignKeySql(mapping!.tableName, foreignKey),\n            )\n            downQueries.push(\n                this.dropForeignKeySql(mapping!.tableName, foreignKey),\n            )\n        })\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(tableOrName: Table | string): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const parsedTableName = this.driver.parseTableName(table)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // SAP HANA does not allow to drop PK's which is referenced by foreign keys.\n        // To avoid this, we must drop all referential foreign keys and recreate them later\n        const referencedForeignKeySql = `SELECT * FROM \"SYS\".\"REFERENTIAL_CONSTRAINTS\" WHERE \"REFERENCED_SCHEMA_NAME\" = '${parsedTableName.schema}' AND \"REFERENCED_TABLE_NAME\" = '${parsedTableName.tableName}'`\n        const dbForeignKeys: ObjectLiteral[] = await this.query(\n            referencedForeignKeySql,\n        )\n        let referencedForeignKeys: TableForeignKey[] = []\n        const referencedForeignKeyTableMapping: {\n            tableName: string\n            fkName: string\n        }[] = []\n        if (dbForeignKeys.length > 0) {\n            referencedForeignKeys = dbForeignKeys.map((dbForeignKey) => {\n                const foreignKeys = dbForeignKeys.filter(\n                    (dbFk) =>\n                        dbFk[\"CONSTRAINT_NAME\"] ===\n                        dbForeignKey[\"CONSTRAINT_NAME\"],\n                )\n\n                referencedForeignKeyTableMapping.push({\n                    tableName: `${dbForeignKey[\"SCHEMA_NAME\"]}.${dbForeignKey[\"TABLE_NAME\"]}`,\n                    fkName: dbForeignKey[\"CONSTRAINT_NAME\"],\n                })\n                return new TableForeignKey({\n                    name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                    columnNames: foreignKeys.map((dbFk) => dbFk[\"COLUMN_NAME\"]),\n                    referencedDatabase: table.database,\n                    referencedSchema: table.schema,\n                    referencedTableName: table.name,\n                    referencedColumnNames: foreignKeys.map(\n                        (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                    ),\n                    onDelete:\n                        dbForeignKey[\"DELETE_RULE\"] === \"RESTRICT\"\n                            ? \"NO ACTION\"\n                            : dbForeignKey[\"DELETE_RULE\"],\n                    onUpdate:\n                        dbForeignKey[\"UPDATE_RULE\"] === \"RESTRICT\"\n                            ? \"NO ACTION\"\n                            : dbForeignKey[\"UPDATE_RULE\"],\n                    deferrable: dbForeignKey[\"CHECK_TIME\"].replace(\"_\", \" \"),\n                })\n            })\n\n            // drop referenced foreign keys\n            referencedForeignKeys.forEach((foreignKey) => {\n                const mapping = referencedForeignKeyTableMapping.find(\n                    (it) => it.fkName === foreignKey.name,\n                )\n                upQueries.push(\n                    this.dropForeignKeySql(mapping!.tableName, foreignKey),\n                )\n                downQueries.push(\n                    this.createForeignKeySql(mapping!.tableName, foreignKey),\n                )\n            })\n        }\n\n        upQueries.push(this.dropPrimaryKeySql(table))\n        downQueries.push(\n            this.createPrimaryKeySql(\n                table,\n                table.primaryColumns.map((column) => column.name),\n            ),\n        )\n\n        // restore referenced foreign keys\n        referencedForeignKeys.forEach((foreignKey) => {\n            const mapping = referencedForeignKeyTableMapping.find(\n                (it) => it.fkName === foreignKey.name,\n            )\n            upQueries.push(\n                this.createForeignKeySql(mapping!.tableName, foreignKey),\n            )\n            downQueries.push(\n                this.dropForeignKeySql(mapping!.tableName, foreignKey),\n            )\n        })\n\n        await this.executeQueries(upQueries, downQueries)\n        table.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n    }\n\n    /**\n     * Creates a new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Creates a new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Drops unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Drops an unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Creates a new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!checkConstraint.name)\n            checkConstraint.name =\n                this.connection.namingStrategy.checkConstraintName(\n                    table,\n                    checkConstraint.expression!,\n                )\n\n        const up = this.createCheckConstraintSql(table, checkConstraint)\n        const down = this.dropCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.addCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Creates a new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.createCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const checkConstraint = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName\n            : table.checks.find((c) => c.name === checkOrName)\n        if (!checkConstraint)\n            throw new TypeORMError(\n                `Supplied check constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropCheckConstraintSql(table, checkConstraint)\n        const down = this.createCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.removeCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.dropCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates a new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SAP HANA does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new FK may be passed without name. In this case we generate FK name manually.\n        if (!foreignKey.name)\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n        const up = this.createForeignKeySql(table, foreignKey)\n        const down = this.dropForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.addForeignKey(foreignKey)\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.createForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const foreignKey = InstanceChecker.isTableForeignKey(foreignKeyOrName)\n            ? foreignKeyOrName\n            : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        const up = this.dropForeignKeySql(table, foreignKey)\n        const down = this.createForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.removeForeignKey(foreignKey)\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.dropForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.createIndexSql(table, index)\n        const down = this.dropIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.addIndex(index)\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.createIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops an index.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in table ${table.name}`,\n            )\n\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(table, index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.dropIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    async clearTable(tablePath: string): Promise<void> {\n        await this.query(`TRUNCATE TABLE ${this.escapePath(tablePath)}`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     */\n    async clearDatabase(): Promise<void> {\n        const schemas: string[] = []\n        this.connection.entityMetadatas\n            .filter((metadata) => metadata.schema)\n            .forEach((metadata) => {\n                const isSchemaExist = !!schemas.find(\n                    (schema) => schema === metadata.schema,\n                )\n                if (!isSchemaExist) schemas.push(metadata.schema!)\n            })\n\n        schemas.push(this.driver.options.schema || \"current_schema\")\n        const schemaNamesString = schemas\n            .map((name) => {\n                return name === \"current_schema\" ? name : \"'\" + name + \"'\"\n            })\n            .join(\", \")\n\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            // const selectViewDropsQuery = `SELECT 'DROP VIEW IF EXISTS \"' || schemaname || '\".\"' || viewname || '\" CASCADE;' as \"query\" ` +\n            //     `FROM \"pg_views\" WHERE \"schemaname\" IN (${schemaNamesString}) AND \"viewname\" NOT IN ('geography_columns', 'geometry_columns', 'raster_columns', 'raster_overviews')`;\n            // const dropViewQueries: ObjectLiteral[] = await this.query(selectViewDropsQuery);\n            // await Promise.all(dropViewQueries.map(q => this.query(q[\"query\"])));\n\n            // ignore spatial_ref_sys; it's a special table supporting PostGIS\n            const selectTableDropsQuery = `SELECT 'DROP TABLE \"' || schema_name || '\".\"' || table_name || '\" CASCADE;' as \"query\" FROM \"SYS\".\"TABLES\" WHERE \"SCHEMA_NAME\" IN (${schemaNamesString}) AND \"TABLE_NAME\" NOT IN ('SYS_AFL_GENERATOR_PARAMETERS') AND \"IS_COLUMN_TABLE\" = 'TRUE'`\n            const dropTableQueries: ObjectLiteral[] = await this.query(\n                selectTableDropsQuery,\n            )\n            await Promise.all(\n                dropTableQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            if (!isAnotherTransactionActive) await this.commitTransaction()\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive)\n                    await this.rollbackTransaction()\n            } catch (rollbackError) {}\n            throw error\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewNames?: string[]): Promise<View[]> {\n        const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n        if (!hasTable) {\n            return []\n        }\n\n        if (!viewNames) {\n            viewNames = []\n        }\n\n        const currentDatabase = await this.getCurrentDatabase()\n        const currentSchema = await this.getCurrentSchema()\n\n        const viewsCondition = viewNames\n            .map((viewName) => {\n                let { schema, tableName: name } =\n                    this.driver.parseTableName(viewName)\n\n                if (!schema) {\n                    schema = currentSchema\n                }\n\n                return `(\"t\".\"schema\" = '${schema}' AND \"t\".\"name\" = '${name}')`\n            })\n            .join(\" OR \")\n\n        const query = `SELECT \"t\".* FROM ${this.escapePath(\n            this.getTypeormMetadataTableName(),\n        )} \"t\" WHERE \"t\".\"type\" = '${MetadataTableType.VIEW}' ${\n            viewsCondition ? `AND (${viewsCondition})` : \"\"\n        }`\n        const dbViews = await this.query(query)\n        return dbViews.map((dbView: any) => {\n            const view = new View()\n            const schema =\n                dbView[\"schema\"] === currentSchema &&\n                !this.driver.options.schema\n                    ? undefined\n                    : dbView[\"schema\"]\n            view.database = currentDatabase\n            view.schema = dbView[\"schema\"]\n            view.name = this.driver.buildTableName(dbView[\"name\"], schema)\n            view.expression = dbView[\"value\"]\n            return view\n        })\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        const currentSchema = await this.getCurrentSchema()\n        const currentDatabase = await this.getCurrentDatabase()\n\n        const dbTables: { SCHEMA_NAME: string; TABLE_NAME: string }[] = []\n\n        if (!tableNames) {\n            const tablesSql = `SELECT \"SCHEMA_NAME\", \"TABLE_NAME\" FROM \"SYS\".\"TABLES\"`\n\n            dbTables.push(...(await this.query(tablesSql)))\n        } else {\n            const tablesCondition = tableNames\n                .map((tableName) => {\n                    let [schema, name] = tableName.split(\".\")\n                    if (!name) {\n                        name = schema\n                        schema = this.driver.options.schema || currentSchema\n                    }\n                    return `(\"SCHEMA_NAME\" = '${schema}' AND \"TABLE_NAME\" = '${name}')`\n                })\n                .join(\" OR \")\n\n            const tablesSql =\n                `SELECT \"SCHEMA_NAME\", \"TABLE_NAME\" FROM \"SYS\".\"TABLES\" WHERE ` +\n                tablesCondition\n\n            dbTables.push(...(await this.query(tablesSql)))\n        }\n\n        // if tables were not found in the db, no need to proceed\n        if (dbTables.length === 0) return []\n\n        const columnsCondition = dbTables\n            .map(({ SCHEMA_NAME, TABLE_NAME }) => {\n                return `(\"SCHEMA_NAME\" = '${SCHEMA_NAME}' AND \"TABLE_NAME\" = '${TABLE_NAME}')`\n            })\n            .join(\" OR \")\n        const columnsSql =\n            `SELECT * FROM \"SYS\".\"TABLE_COLUMNS\" WHERE ` +\n            columnsCondition +\n            ` ORDER BY \"POSITION\"`\n\n        const constraintsCondition = dbTables\n            .map(({ SCHEMA_NAME, TABLE_NAME }) => {\n                return `(\"SCHEMA_NAME\" = '${SCHEMA_NAME}' AND \"TABLE_NAME\" = '${TABLE_NAME}')`\n            })\n            .join(\" OR \")\n        const constraintsSql = `SELECT * FROM \"SYS\".\"CONSTRAINTS\" WHERE (${constraintsCondition}) ORDER BY \"POSITION\"`\n\n        const indicesCondition = dbTables\n            .map(({ SCHEMA_NAME, TABLE_NAME }) => {\n                return `(\"I\".\"SCHEMA_NAME\" = '${SCHEMA_NAME}' AND \"I\".\"TABLE_NAME\" = '${TABLE_NAME}')`\n            })\n            .join(\" OR \")\n        // excluding primary key and autogenerated fulltext indices\n        const indicesSql =\n            `SELECT \"I\".\"INDEX_TYPE\", \"I\".\"SCHEMA_NAME\", \"I\".\"TABLE_NAME\", \"I\".\"INDEX_NAME\", \"IC\".\"COLUMN_NAME\", \"I\".\"CONSTRAINT\" ` +\n            `FROM \"SYS\".\"INDEXES\" \"I\" INNER JOIN \"SYS\".\"INDEX_COLUMNS\" \"IC\" ON \"IC\".\"INDEX_OID\" = \"I\".\"INDEX_OID\" ` +\n            `WHERE (${indicesCondition}) AND (\"I\".\"CONSTRAINT\" IS NULL OR \"I\".\"CONSTRAINT\" != 'PRIMARY KEY') AND \"I\".\"INDEX_NAME\" NOT LIKE '%_SYS_FULLTEXT_%' ORDER BY \"IC\".\"POSITION\"`\n\n        const foreignKeysCondition = dbTables\n            .map(({ SCHEMA_NAME, TABLE_NAME }) => {\n                return `(\"SCHEMA_NAME\" = '${SCHEMA_NAME}' AND \"TABLE_NAME\" = '${TABLE_NAME}')`\n            })\n            .join(\" OR \")\n        const foreignKeysSql = `SELECT * FROM \"SYS\".\"REFERENTIAL_CONSTRAINTS\" WHERE (${foreignKeysCondition}) ORDER BY \"POSITION\"`\n        const [\n            dbColumns,\n            dbConstraints,\n            dbIndices,\n            dbForeignKeys,\n        ]: ObjectLiteral[][] = await Promise.all([\n            this.query(columnsSql),\n            this.query(constraintsSql),\n            this.query(indicesSql),\n            this.query(foreignKeysSql),\n        ])\n\n        // create tables for loaded tables\n        return Promise.all(\n            dbTables.map(async (dbTable) => {\n                const table = new Table()\n                const getSchemaFromKey = (dbObject: any, key: string) => {\n                    return dbObject[key] === currentSchema &&\n                        (!this.driver.options.schema ||\n                            this.driver.options.schema === currentSchema)\n                        ? undefined\n                        : dbObject[key]\n                }\n\n                // We do not need to join schema name, when database is by default.\n                const schema = getSchemaFromKey(dbTable, \"SCHEMA_NAME\")\n                table.database = currentDatabase\n                table.schema = dbTable[\"SCHEMA_NAME\"]\n                table.name = this.driver.buildTableName(\n                    dbTable[\"TABLE_NAME\"],\n                    schema,\n                )\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns\n                        .filter(\n                            (dbColumn) =>\n                                dbColumn[\"TABLE_NAME\"] ===\n                                    dbTable[\"TABLE_NAME\"] &&\n                                dbColumn[\"SCHEMA_NAME\"] ===\n                                    dbTable[\"SCHEMA_NAME\"],\n                        )\n                        .map(async (dbColumn) => {\n                            const columnConstraints = dbConstraints.filter(\n                                (dbConstraint) =>\n                                    dbConstraint[\"TABLE_NAME\"] ===\n                                        dbColumn[\"TABLE_NAME\"] &&\n                                    dbConstraint[\"SCHEMA_NAME\"] ===\n                                        dbColumn[\"SCHEMA_NAME\"] &&\n                                    dbConstraint[\"COLUMN_NAME\"] ===\n                                        dbColumn[\"COLUMN_NAME\"],\n                            )\n\n                            const columnUniqueIndices = dbIndices.filter(\n                                (dbIndex) => {\n                                    return (\n                                        dbIndex[\"TABLE_NAME\"] ===\n                                            dbTable[\"TABLE_NAME\"] &&\n                                        dbIndex[\"SCHEMA_NAME\"] ===\n                                            dbTable[\"SCHEMA_NAME\"] &&\n                                        dbIndex[\"COLUMN_NAME\"] ===\n                                            dbColumn[\"COLUMN_NAME\"] &&\n                                        dbIndex[\"CONSTRAINT\"] &&\n                                        dbIndex[\"CONSTRAINT\"].indexOf(\n                                            \"UNIQUE\",\n                                        ) !== -1\n                                    )\n                                },\n                            )\n\n                            const tableMetadata =\n                                this.connection.entityMetadatas.find(\n                                    (metadata) =>\n                                        this.getTablePath(table) ===\n                                        this.getTablePath(metadata),\n                                )\n                            const hasIgnoredIndex =\n                                columnUniqueIndices.length > 0 &&\n                                tableMetadata &&\n                                tableMetadata.indices.some((index) => {\n                                    return columnUniqueIndices.some(\n                                        (uniqueIndex) => {\n                                            return (\n                                                index.name ===\n                                                    uniqueIndex[\"INDEX_NAME\"] &&\n                                                index.synchronize === false\n                                            )\n                                        },\n                                    )\n                                })\n\n                            const isConstraintComposite =\n                                columnUniqueIndices.every((uniqueIndex) => {\n                                    return dbIndices.some(\n                                        (dbIndex) =>\n                                            dbIndex[\"INDEX_NAME\"] ===\n                                                uniqueIndex[\"INDEX_NAME\"] &&\n                                            dbIndex[\"COLUMN_NAME\"] !==\n                                                dbColumn[\"COLUMN_NAME\"],\n                                    )\n                                })\n\n                            const tableColumn = new TableColumn()\n                            tableColumn.name = dbColumn[\"COLUMN_NAME\"]\n                            tableColumn.type =\n                                dbColumn[\"DATA_TYPE_NAME\"].toLowerCase()\n\n                            if (\n                                tableColumn.type === \"dec\" ||\n                                tableColumn.type === \"decimal\"\n                            ) {\n                                // If one of these properties was set, and another was not, Postgres sets '0' in to unspecified property\n                                // we set 'undefined' in to unspecified property to avoid changing column on sync\n                                if (\n                                    dbColumn[\"LENGTH\"] !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"LENGTH\"],\n                                    )\n                                ) {\n                                    tableColumn.precision = dbColumn[\"LENGTH\"]\n                                } else if (\n                                    dbColumn[\"SCALE\"] !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"SCALE\"],\n                                    )\n                                ) {\n                                    tableColumn.precision = undefined\n                                }\n                                if (\n                                    dbColumn[\"SCALE\"] !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"SCALE\"],\n                                    )\n                                ) {\n                                    tableColumn.scale = dbColumn[\"SCALE\"]\n                                } else if (\n                                    dbColumn[\"LENGTH\"] !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"LENGTH\"],\n                                    )\n                                ) {\n                                    tableColumn.scale = undefined\n                                }\n                            }\n\n                            if (\n                                dbColumn[\"DATA_TYPE_NAME\"].toLowerCase() ===\n                                \"array\"\n                            ) {\n                                tableColumn.isArray = true\n                                tableColumn.type =\n                                    dbColumn[\"CS_DATA_TYPE_NAME\"].toLowerCase()\n                            }\n\n                            // check only columns that have length property\n                            if (\n                                this.driver.withLengthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1 &&\n                                dbColumn[\"LENGTH\"]\n                            ) {\n                                const length = dbColumn[\"LENGTH\"].toString()\n                                tableColumn.length =\n                                    !this.isDefaultColumnLength(\n                                        table,\n                                        tableColumn,\n                                        length,\n                                    )\n                                        ? length\n                                        : \"\"\n                            }\n                            tableColumn.isUnique =\n                                columnUniqueIndices.length > 0 &&\n                                !hasIgnoredIndex &&\n                                !isConstraintComposite\n                            tableColumn.isNullable =\n                                dbColumn[\"IS_NULLABLE\"] === \"TRUE\"\n                            tableColumn.isPrimary = !!columnConstraints.find(\n                                (constraint) =>\n                                    constraint[\"IS_PRIMARY_KEY\"] === \"TRUE\",\n                            )\n                            tableColumn.isGenerated =\n                                dbColumn[\"GENERATION_TYPE\"] ===\n                                \"ALWAYS AS IDENTITY\"\n                            if (tableColumn.isGenerated)\n                                tableColumn.generationStrategy = \"increment\"\n\n                            if (\n                                dbColumn[\"DEFAULT_VALUE\"] === null ||\n                                dbColumn[\"DEFAULT_VALUE\"] === undefined\n                            ) {\n                                tableColumn.default = undefined\n                            } else {\n                                if (\n                                    tableColumn.type === \"char\" ||\n                                    tableColumn.type === \"nchar\" ||\n                                    tableColumn.type === \"varchar\" ||\n                                    tableColumn.type === \"nvarchar\" ||\n                                    tableColumn.type === \"alphanum\" ||\n                                    tableColumn.type === \"shorttext\"\n                                ) {\n                                    tableColumn.default = `'${dbColumn[\"DEFAULT_VALUE\"]}'`\n                                } else if (tableColumn.type === \"boolean\") {\n                                    tableColumn.default =\n                                        dbColumn[\"DEFAULT_VALUE\"] === \"1\"\n                                            ? \"true\"\n                                            : \"false\"\n                                } else {\n                                    tableColumn.default =\n                                        dbColumn[\"DEFAULT_VALUE\"]\n                                }\n                            }\n                            if (dbColumn[\"COMMENTS\"]) {\n                                tableColumn.comment = dbColumn[\"COMMENTS\"]\n                            }\n                            return tableColumn\n                        }),\n                )\n\n                // find check constraints of table, group them by constraint name and build TableCheck.\n                const tableCheckConstraints = OrmUtils.uniq(\n                    dbConstraints.filter(\n                        (dbConstraint) =>\n                            dbConstraint[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbConstraint[\"SCHEMA_NAME\"] ===\n                                dbTable[\"SCHEMA_NAME\"] &&\n                            dbConstraint[\"CHECK_CONDITION\"] !== null &&\n                            dbConstraint[\"CHECK_CONDITION\"] !== undefined,\n                    ),\n                    (dbConstraint) => dbConstraint[\"CONSTRAINT_NAME\"],\n                )\n\n                table.checks = tableCheckConstraints.map((constraint) => {\n                    const checks = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"CONSTRAINT_NAME\"] ===\n                            constraint[\"CONSTRAINT_NAME\"],\n                    )\n                    return new TableCheck({\n                        name: constraint[\"CONSTRAINT_NAME\"],\n                        columnNames: checks.map((c) => c[\"COLUMN_NAME\"]),\n                        expression: constraint[\"CHECK_CONDITION\"],\n                    })\n                })\n\n                // find foreign key constraints of table, group them by constraint name and build TableForeignKey.\n                const tableForeignKeyConstraints = OrmUtils.uniq(\n                    dbForeignKeys.filter(\n                        (dbForeignKey) =>\n                            dbForeignKey[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbForeignKey[\"SCHEMA_NAME\"] ===\n                                dbTable[\"SCHEMA_NAME\"],\n                    ),\n                    (dbForeignKey) => dbForeignKey[\"CONSTRAINT_NAME\"],\n                )\n\n                table.foreignKeys = tableForeignKeyConstraints.map(\n                    (dbForeignKey) => {\n                        const foreignKeys = dbForeignKeys.filter(\n                            (dbFk) =>\n                                dbFk[\"CONSTRAINT_NAME\"] ===\n                                dbForeignKey[\"CONSTRAINT_NAME\"],\n                        )\n\n                        // if referenced table located in currently used schema, we don't need to concat schema name to table name.\n                        const schema = getSchemaFromKey(\n                            dbForeignKey,\n                            \"REFERENCED_SCHEMA_NAME\",\n                        )\n                        const referencedTableName = this.driver.buildTableName(\n                            dbForeignKey[\"REFERENCED_TABLE_NAME\"],\n                            schema,\n                        )\n\n                        return new TableForeignKey({\n                            name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                            columnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"COLUMN_NAME\"],\n                            ),\n                            referencedDatabase: table.database,\n                            referencedSchema:\n                                dbForeignKey[\"REFERENCED_SCHEMA_NAME\"],\n                            referencedTableName: referencedTableName,\n                            referencedColumnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                            ),\n                            onDelete:\n                                dbForeignKey[\"DELETE_RULE\"] === \"RESTRICT\"\n                                    ? \"NO ACTION\"\n                                    : dbForeignKey[\"DELETE_RULE\"],\n                            onUpdate:\n                                dbForeignKey[\"UPDATE_RULE\"] === \"RESTRICT\"\n                                    ? \"NO ACTION\"\n                                    : dbForeignKey[\"UPDATE_RULE\"],\n                            deferrable: dbForeignKey[\"CHECK_TIME\"].replace(\n                                \"_\",\n                                \" \",\n                            ),\n                        })\n                    },\n                )\n\n                // find index constraints of table, group them by constraint name and build TableIndex.\n                const tableIndexConstraints = OrmUtils.uniq(\n                    dbIndices.filter(\n                        (dbIndex) =>\n                            dbIndex[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"] &&\n                            dbIndex[\"SCHEMA_NAME\"] === dbTable[\"SCHEMA_NAME\"],\n                    ),\n                    (dbIndex) => dbIndex[\"INDEX_NAME\"],\n                )\n\n                table.indices = tableIndexConstraints.map((constraint) => {\n                    const indices = dbIndices.filter((index) => {\n                        return (\n                            index[\"SCHEMA_NAME\"] ===\n                                constraint[\"SCHEMA_NAME\"] &&\n                            index[\"TABLE_NAME\"] === constraint[\"TABLE_NAME\"] &&\n                            index[\"INDEX_NAME\"] === constraint[\"INDEX_NAME\"]\n                        )\n                    })\n                    return new TableIndex(<TableIndexOptions>{\n                        table: table,\n                        name: constraint[\"INDEX_NAME\"],\n                        columnNames: indices.map((i) => i[\"COLUMN_NAME\"]),\n                        isUnique:\n                            constraint[\"CONSTRAINT\"] &&\n                            constraint[\"CONSTRAINT\"].indexOf(\"UNIQUE\") !== -1,\n                        isFulltext: constraint[\"INDEX_TYPE\"] === \"FULLTEXT\",\n                    })\n                })\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds and returns SQL for create table.\n     */\n    protected createTableSql(table: Table, createForeignKeys?: boolean): Query {\n        const columnDefinitions = table.columns\n            .map((column) => this.buildCreateColumnSql(column))\n            .join(\", \")\n        let sql = `CREATE TABLE ${this.escapePath(table)} (${columnDefinitions}`\n\n        // we create unique indexes instead of unique constraints, because SAP HANA does not have unique constraints.\n        // if we mark column as Unique, it means that we create UNIQUE INDEX.\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueIndexExist = table.indices.some((index) => {\n                    return (\n                        index.columnNames.length === 1 &&\n                        !!index.isUnique &&\n                        index.columnNames.indexOf(column.name) !== -1\n                    )\n                })\n                const isUniqueConstraintExist = table.uniques.some((unique) => {\n                    return (\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames.indexOf(column.name) !== -1\n                    )\n                })\n                if (!isUniqueIndexExist && !isUniqueConstraintExist)\n                    table.indices.push(\n                        new TableIndex({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                            isUnique: true,\n                        }),\n                    )\n            })\n\n        // as SAP HANA does not have unique constraints, we must create table indices from table uniques and mark them as unique.\n        if (table.uniques.length > 0) {\n            table.uniques.forEach((unique) => {\n                const uniqueExist = table.indices.some(\n                    (index) => index.name === unique.name,\n                )\n                if (!uniqueExist) {\n                    table.indices.push(\n                        new TableIndex({\n                            name: unique.name,\n                            columnNames: unique.columnNames,\n                            isUnique: true,\n                        }),\n                    )\n                }\n            })\n        }\n\n        if (table.checks.length > 0) {\n            const checksSql = table.checks\n                .map((check) => {\n                    const checkName = check.name\n                        ? check.name\n                        : this.connection.namingStrategy.checkConstraintName(\n                              table,\n                              check.expression!,\n                          )\n                    return `CONSTRAINT \"${checkName}\" CHECK (${check.expression})`\n                })\n                .join(\", \")\n\n            sql += `, ${checksSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .map((fk) => {\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            table,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n\n                    let constraint = `CONSTRAINT \"${\n                        fk.name\n                    }\" FOREIGN KEY (${columnNames}) REFERENCES ${this.escapePath(\n                        this.getTablePath(fk),\n                    )} (${referencedColumnNames})`\n                    // SAP HANA does not have \"NO ACTION\" option for FK's\n                    if (fk.onDelete) {\n                        const onDelete =\n                            fk.onDelete === \"NO ACTION\"\n                                ? \"RESTRICT\"\n                                : fk.onDelete\n                        constraint += ` ON DELETE ${onDelete}`\n                    }\n                    if (fk.onUpdate) {\n                        const onUpdate =\n                            fk.onUpdate === \"NO ACTION\"\n                                ? \"RESTRICT\"\n                                : fk.onUpdate\n                        constraint += ` ON UPDATE ${onUpdate}`\n                    }\n                    if (fk.deferrable) {\n                        constraint += ` ${fk.deferrable}`\n                    }\n\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        const primaryColumns = table.columns.filter(\n            (column) => column.isPrimary,\n        )\n        if (primaryColumns.length > 0) {\n            const primaryKeyName =\n                this.connection.namingStrategy.primaryKeyName(\n                    table,\n                    primaryColumns.map((column) => column.name),\n                )\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            sql += `, CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNames})`\n        }\n\n        sql += `)`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop table sql.\n     */\n    protected dropTableSql(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n    ): Query {\n        const query = ifExist\n            ? `DROP TABLE IF EXISTS ${this.escapePath(tableOrName)}`\n            : `DROP TABLE ${this.escapePath(tableOrName)}`\n        return new Query(query)\n    }\n\n    protected createViewSql(view: View): Query {\n        if (typeof view.expression === \"string\") {\n            return new Query(\n                `CREATE VIEW ${this.escapePath(view)} AS ${view.expression}`,\n            )\n        } else {\n            return new Query(\n                `CREATE VIEW ${this.escapePath(view)} AS ${view\n                    .expression(this.connection)\n                    .getQuery()}`,\n            )\n        }\n    }\n\n    protected async insertViewDefinitionSql(view: View): Promise<Query> {\n        let { schema, tableName: name } = this.driver.parseTableName(view)\n\n        if (!schema) {\n            schema = await this.getCurrentSchema()\n        }\n\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        return this.insertTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            schema: schema,\n            name: name,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(viewOrPath: View | string): Query {\n        return new Query(`DROP VIEW ${this.escapePath(viewOrPath)}`)\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected async deleteViewDefinitionSql(\n        viewOrPath: View | string,\n    ): Promise<Query> {\n        let { schema, tableName: name } = this.driver.parseTableName(viewOrPath)\n\n        if (!schema) {\n            schema = await this.getCurrentSchema()\n        }\n\n        return this.deleteTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            schema,\n            name,\n        })\n    }\n\n    protected addColumnSql(table: Table, column: TableColumn): string {\n        return `ALTER TABLE ${this.escapePath(\n            table,\n        )} ADD (${this.buildCreateColumnSql(column)})`\n    }\n\n    protected dropColumnSql(table: Table, column: TableColumn): string {\n        return `ALTER TABLE ${this.escapePath(table)} DROP (\"${column.name}\")`\n    }\n\n    /**\n     * Builds create index sql.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        let indexType = \"\"\n        if (index.isUnique) {\n            indexType += \"UNIQUE \"\n        }\n        if (index.isFulltext && this.driver.isFullTextColumnTypeSupported()) {\n            indexType += \"FULLTEXT \"\n        }\n\n        return new Query(\n            `CREATE ${indexType}INDEX \"${index.name}\" ON ${this.escapePath(\n                table,\n            )} (${columns}) ${index.where ? \"WHERE \" + index.where : \"\"}`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(\n        table: Table,\n        indexOrName: TableIndex | string,\n    ): Query {\n        const indexName = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName.name\n            : indexOrName\n        const parsedTableName = this.driver.parseTableName(table)\n\n        if (!parsedTableName.schema) {\n            return new Query(`DROP INDEX \"${indexName}\"`)\n        } else {\n            return new Query(\n                `DROP INDEX \"${parsedTableName.schema}\".\"${indexName}\"`,\n            )\n        }\n    }\n\n    /**\n     * Builds create primary key sql.\n     */\n    protected createPrimaryKeySql(table: Table, columnNames: string[]): Query {\n        const primaryKeyName = this.connection.namingStrategy.primaryKeyName(\n            table,\n            columnNames,\n        )\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} ADD CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNamesString})`,\n        )\n    }\n\n    /**\n     * Builds drop primary key sql.\n     */\n    protected dropPrimaryKeySql(table: Table): Query {\n        const columnNames = table.primaryColumns.map((column) => column.name)\n        const primaryKeyName = this.connection.namingStrategy.primaryKeyName(\n            table,\n            columnNames,\n        )\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${primaryKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds create check constraint sql.\n     */\n    protected createCheckConstraintSql(\n        table: Table,\n        checkConstraint: TableCheck,\n    ): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                checkConstraint.name\n            }\" CHECK (${checkConstraint.expression})`,\n        )\n    }\n\n    /**\n     * Builds drop check constraint sql.\n     */\n    protected dropCheckConstraintSql(\n        table: Table,\n        checkOrName: TableCheck | string,\n    ): Query {\n        const checkName = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName.name\n            : checkOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${checkName}\"`,\n        )\n    }\n\n    /**\n     * Builds create foreign key sql.\n     */\n    protected createForeignKeySql(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Query {\n        const columnNames = foreignKey.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        const referencedColumnNames = foreignKey.referencedColumnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\",\")\n        let sql =\n            `ALTER TABLE ${this.escapePath(tableOrName)} ADD CONSTRAINT \"${\n                foreignKey.name\n            }\" FOREIGN KEY (${columnNames}) ` +\n            `REFERENCES ${this.escapePath(\n                this.getTablePath(foreignKey),\n            )}(${referencedColumnNames})`\n\n        // SAP HANA does not have \"NO ACTION\" option for FK's\n        if (foreignKey.onDelete) {\n            const onDelete =\n                foreignKey.onDelete === \"NO ACTION\"\n                    ? \"RESTRICT\"\n                    : foreignKey.onDelete\n            sql += ` ON DELETE ${onDelete}`\n        }\n        if (foreignKey.onUpdate) {\n            const onUpdate =\n                foreignKey.onUpdate === \"NO ACTION\"\n                    ? \"RESTRICT\"\n                    : foreignKey.onUpdate\n            sql += ` ON UPDATE ${onUpdate}`\n        }\n\n        if (foreignKey.deferrable) {\n            sql += ` ${foreignKey.deferrable}`\n        }\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop foreign key sql.\n     */\n    protected dropForeignKeySql(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Query {\n        const foreignKeyName = InstanceChecker.isTableForeignKey(\n            foreignKeyOrName,\n        )\n            ? foreignKeyOrName.name\n            : foreignKeyOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                tableOrName,\n            )} DROP CONSTRAINT \"${foreignKeyName}\"`,\n        )\n    }\n\n    /**\n     * Escapes a given comment so it's safe to include in a query.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment) {\n            return \"NULL\"\n        }\n\n        comment = comment.replace(/'/g, \"''\").replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return `'${comment}'`\n    }\n\n    /**\n     * Escapes given table or view path.\n     */\n    protected escapePath(target: Table | View | string): string {\n        const { schema, tableName } = this.driver.parseTableName(target)\n\n        if (schema) {\n            return `\"${schema}\".\"${tableName}\"`\n        }\n\n        return `\"${tableName}\"`\n    }\n\n    /**\n     * Builds a query for create column.\n     */\n    protected buildCreateColumnSql(\n        column: TableColumn,\n        explicitDefault?: boolean,\n        explicitNullable?: boolean,\n    ) {\n        let c =\n            `\"${column.name}\" ` + this.connection.driver.createFullType(column)\n        if (column.default !== undefined && column.default !== null) {\n            c += \" DEFAULT \" + column.default\n        } else if (explicitDefault) {\n            c += \" DEFAULT NULL\"\n        }\n        if (!column.isGenerated) {\n            // NOT NULL is not supported with GENERATED\n            if (column.isNullable !== true) c += \" NOT NULL\"\n            else if (explicitNullable) c += \" NULL\"\n        }\n        if (\n            column.isGenerated === true &&\n            column.generationStrategy === \"increment\"\n        ) {\n            c += \" GENERATED ALWAYS AS IDENTITY\"\n        }\n        if (column.comment) {\n            c += ` COMMENT ${this.escapeComment(column.comment)}`\n        }\n\n        return c\n    }\n\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `spa driver does not support change table comment.`,\n        )\n    }\n}\n"], "sourceRoot": "../.."}