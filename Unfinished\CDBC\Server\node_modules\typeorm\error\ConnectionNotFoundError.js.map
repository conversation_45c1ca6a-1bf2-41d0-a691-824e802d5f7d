{"version": 3, "sources": ["../../src/error/ConnectionNotFoundError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,uBAAwB,SAAQ,2BAAY;IACrD,YAAY,IAAY;QACpB,KAAK,CAAC,eAAe,IAAI,kBAAkB,CAAC,CAAA;IAChD,CAAC;CACJ;AAJD,0DAIC", "file": "ConnectionNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to get connection that does not exist.\n */\nexport class ConnectionNotFoundError extends TypeORMError {\n    constructor(name: string) {\n        super(`Connection \"${name}\" was not found.`)\n    }\n}\n"], "sourceRoot": ".."}