"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LimitOnUpdateNotSupportedError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when user tries to build an UPDATE query with LIMIT but the database does not support it.
 */
class LimitOnUpdateNotSupportedError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`Your database does not support LIMIT on UPDATE statements.`);
    }
}
exports.LimitOnUpdateNotSupportedError = LimitOnUpdateNotSupportedError;

//# sourceMappingURL=LimitOnUpdateNotSupportedError.js.map
