{"version": 3, "sources": ["../../src/error/InsertValuesMissingError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,wBAAyB,SAAQ,2BAAY;IACtD;QACI,KAAK,CACD,8DAA8D;YAC1D,0DAA0D,CACjE,CAAA;IACL,CAAC;CACJ;AAPD,4DAOC", "file": "InsertValuesMissingError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to insert using QueryBuilder but do not specify what to insert.\n */\nexport class InsertValuesMissingError extends TypeORMError {\n    constructor() {\n        super(\n            `Cannot perform insert query because values are not defined. ` +\n                `Call \"qb.values(...)\" method to specify inserted values.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}