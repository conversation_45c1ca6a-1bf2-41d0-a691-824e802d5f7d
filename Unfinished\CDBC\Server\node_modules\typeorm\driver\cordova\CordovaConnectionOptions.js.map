{"version": 3, "sources": ["../../src/driver/cordova/CordovaConnectionOptions.ts"], "names": [], "mappings": "", "file": "CordovaConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Sqlite-specific connection options.\n */\nexport interface CordovaConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"cordova\"\n\n    /**\n     * Database name.\n     */\n    readonly database: string\n\n    /**\n     * The driver object\n     * This defaults to `window.sqlitePlugin`\n     */\n    readonly driver?: any\n\n    /**\n     * Storage Location\n     */\n    readonly location: string\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}