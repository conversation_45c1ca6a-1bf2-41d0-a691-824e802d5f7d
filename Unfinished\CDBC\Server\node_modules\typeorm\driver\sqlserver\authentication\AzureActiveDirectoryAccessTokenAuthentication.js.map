{"version": 3, "sources": ["../../src/driver/sqlserver/authentication/AzureActiveDirectoryAccessTokenAuthentication.ts"], "names": [], "mappings": "", "file": "AzureActiveDirectoryAccessTokenAuthentication.js", "sourcesContent": ["export interface AzureActiveDirectoryAccessTokenAuthentication {\n    type: \"azure-active-directory-access-token\"\n    options: {\n        /**\n         * A user need to provide `token` which they retrieved else where\n         * to forming the connection.\n         */\n        token: string\n    }\n}\n"], "sourceRoot": "../../.."}