{"version": 3, "sources": ["../../src/error/QueryFailedError.ts"], "names": [], "mappings": ";;;AAAA,qDAAiD;AACjD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,gBAA0C,SAAQ,2BAAY;IACvE,YACa,KAAa,EACb,UAA6B,EAC7B,WAAc;QAEvB,KAAK,CACD,WAAW;aACN,QAAQ,EAAE;aACV,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;aACvB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;aACvB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAC/B,CAAA;QAVQ,UAAK,GAAL,KAAK,CAAQ;QACb,eAAU,GAAV,UAAU,CAAmB;QAC7B,gBAAW,GAAX,WAAW,CAAG;QAUvB,IAAI,WAAW,EAAE,CAAC;YACd,MAAM,EACF,IAAI,EAAE,CAAC,EAAE,sBAAsB;YAC/B,GAAG,eAAe,EACrB,GAAG,WAAW,CAAA;YAEf,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE;gBACrB,GAAG,eAAe;aACrB,CAAC,CAAA;QACN,CAAC;IACL,CAAC;CACJ;AAzBD,4CAyBC", "file": "QueryFailedError.js", "sourcesContent": ["import { ObjectUtils } from \"../util/ObjectUtils\"\nimport { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when query execution has failed.\n */\nexport class QueryFailedError<T extends Error = Error> extends TypeORMError {\n    constructor(\n        readonly query: string,\n        readonly parameters: any[] | undefined,\n        readonly driverError: T,\n    ) {\n        super(\n            driverError\n                .toString()\n                .replace(/^error: /, \"\")\n                .replace(/^Error: /, \"\")\n                .replace(/^Request/, \"\"),\n        )\n\n        if (driverError) {\n            const {\n                name: _, // eslint-disable-line\n                ...otherProperties\n            } = driverError\n\n            ObjectUtils.assign(this, {\n                ...otherProperties,\n            })\n        }\n    }\n}\n"], "sourceRoot": ".."}