{"version": 3, "sources": ["../../src/driver/types/MetadataTableType.ts"], "names": [], "mappings": ";;;AAAA,IAAY,iBAIX;AAJD,WAAY,iBAAiB;IACzB,kCAAa,CAAA;IACb,4DAAuC,CAAA;IACvC,0DAAqC,CAAA;AACzC,CAAC,EAJW,iBAAiB,iCAAjB,iBAAiB,QAI5B", "file": "MetadataTableType.js", "sourcesContent": ["export enum MetadataTableType {\n    VIEW = \"VIEW\",\n    MATERIALIZED_VIEW = \"MATERIALIZED_VIEW\",\n    GENERATED_COLUMN = \"GENERATED_COLUMN\",\n}\n"], "sourceRoot": "../.."}