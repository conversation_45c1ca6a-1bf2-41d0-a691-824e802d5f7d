{"version": 3, "sources": ["../../src/driver/DriverUtils.ts"], "names": [], "mappings": ";;;AACA,qDAAmD;AACnD,uDAAmD;AAEnD;;GAEG;AACH,MAAa,WAAW;IACpB,4EAA4E;IAC5E,wBAAwB;IACxB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,MAAc;QAChC,OAAO;YACH,QAAQ;YACR,SAAS;YACT,cAAc;YACd,cAAc;YACd,OAAO;YACP,MAAM;YACN,gBAAgB;YAChB,WAAW;SACd,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,MAAc;QAC/B,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC7D,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,MAAc,EAAE,OAAe;QAC5D,OAAO,2BAAY,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IACjE,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,MAAc;QAClC,OAAO,CAAC,UAAU,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC,QAAQ,CAC1D,MAAM,CAAC,OAAO,CAAC,IAAI,CACtB,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,kBAAkB,CACrB,OAAY,EACZ,YAAkC;QAElC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAE3D,CAAA;YAED,IACI,YAAY;gBACZ,YAAY,CAAC,MAAM;gBACnB,gBAAgB,CAAC,QAAQ,EAC3B,CAAC;gBACC,gBAAgB,CAAC,GAAG,GAAG,gBAAgB,CAAC,QAAQ,CAAA;YACpD,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC9C,IAAI,OAAO,gBAAgB,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE,CAAC;oBAC/C,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;gBAChC,CAAC;YACL,CAAC;YAED,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAA;QACvD,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB,CAC5B,OAAY,EACZ,YAAkC;QAElC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CACnD,OAAO,CAAC,GAAG,CACY,CAAA;YAE3B,IACI,YAAY;gBACZ,YAAY,CAAC,MAAM;gBACnB,gBAAgB,CAAC,QAAQ,EAC3B,CAAC;gBACC,gBAAgB,CAAC,GAAG,GAAG,gBAAgB,CAAC,QAAQ,CAAA;YACpD,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC9C,IAAI,OAAO,gBAAgB,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE,CAAC;oBAC/C,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;gBAChC,CAAC;YACL,CAAC;YAED,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAA;QACvD,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;IACrC,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,MAAM,CAAC,UAAU,CACb,EAAE,cAAc,EAAU,EAC1B,YAAgE,EAChE,GAAG,KAAe;QAElB,MAAM,MAAM,GACR,YAAY,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAA;QAEnE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAEnE,IACI,cAAc;YACd,cAAc,GAAG,CAAC;YAClB,QAAQ,CAAC,MAAM,GAAG,cAAc,EAClC,CAAC;YACC,IAAI,YAAY,IAAI,YAAY,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBAChD,MAAM,cAAc,GAAG,IAAA,qBAAO,EAAC,QAAQ,CAAC,CAAA;gBACxC,IAAI,cAAc,CAAC,MAAM,GAAG,cAAc,EAAE,CAAC;oBACzC,OAAO,cAAc,CAAA;gBACzB,CAAC;YACL,CAAC;YAED,OAAO,IAAA,kBAAI,EAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAA;QACrD,CAAC;QAED,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACnB,EAAE,cAAc,EAAU,EAC1B,YAA6D,EAC7D,GAAG,KAAe;QAElB,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;YAC3B,YAAY,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,CAAA;QAClD,CAAC;aAAM,CAAC;YACJ,YAAY,GAAG,MAAM,CAAC,MAAM,CACxB,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,EAC/B,YAAY,CACf,CAAA;QACL,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAClB,EAAE,cAAc,EAAY,EAC5B,YAAY,EACZ,GAAG,KAAK,CACX,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,yBAAyB;IACzB,4EAA4E;IAE5E;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,GAAW;QACzC,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAC9B,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACtC,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QACxC,MAAM,IAAI,GACN,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QACjE,IAAI,SAAS,GACT,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACpE,8BAA8B;QAC9B,IAAI,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7C,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QACxC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QAE/C,IAAI,QAAQ,GAAG,mBAAmB,CAAA;QAClC,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,MAAM,UAAU,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QACnD,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YACpB,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;YACpD,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QACzD,CAAC;QACD,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE3C,OAAO;YACH,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC;YACtC,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC;YACtC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACvC,QAAQ,EAAE,SAAS,IAAI,SAAS;SACnC,CAAA;IACL,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CAAC,GAAW;QAChD,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAC9B,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACtC,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QACxC,MAAM,IAAI,GACN,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QACjE,IAAI,SAAS,GACT,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACpE,IAAI,iBAAiB,GAAG,EAAE,CAAA;QAC1B,IAAI,IAAI,GAAG,SAAS,CAAA;QACpB,IAAI,IAAI,GAAG,SAAS,CAAA;QACpB,IAAI,cAAc,GAAG,SAAS,CAAA;QAC9B,IAAI,UAAU,GAAG,SAAS,CAAA;QAE1B,MAAM,aAAa,GAAQ,EAAE,CAAA;QAE7B,IAAI,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7C,eAAe;YACf,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAChC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAC1B,SAAS,CAAC,MAAM,CACnB,CAAA;YAED,MAAM,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAChD,IAAI,SAAiB,CAAA;YACrB,IAAI,WAAmB,CAAA;YAEvB,yEAAyE;YACzE,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC/B,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;gBACpC,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;gBACtC,aAAa,CAAC,SAAS,CAAC,GAAG,WAAW,CAAA;YAC1C,CAAC,CAAC,CAAA;YAEF,gEAAgE;YAChE,UAAU,GAAG,aAAa,CAAC,YAAY,CAAC,CAAA;YACxC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QACxC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QAE/C,IAAI,QAAQ,GAAG,mBAAmB,CAAA;QAClC,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,MAAM,UAAU,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QACnD,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YACpB,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;YACpD,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QACzD,CAAC;QAED,+EAA+E;QAC/E,IAAI,UAAU,EAAE,CAAC;YACb,cAAc,GAAG,WAAW,CAAA;QAChC,CAAC;aAAM,CAAC;YACJ,CAAC;YAAA,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC1C,CAAC;QAED,MAAM,aAAa,GAAQ;YACvB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,cAAc,EAAE,cAAc;YAC9B,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC;YACtC,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC;YACtC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACvC,QAAQ,EAAE,SAAS,IAAI,SAAS;SACnC,CAAA;QAED,uDAAuD;QACvD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YACvD,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;QAC9B,CAAC;QAED,OAAO,aAAa,CAAA;IACxB,CAAC;CACJ;AAjSD,kCAiSC", "file": "DriverUtils.js", "sourcesContent": ["import { Driver } from \"./Driver\"\nimport { hash, shorten } from \"../util/StringUtils\"\nimport { VersionUtils } from \"../util/VersionUtils\"\n\n/**\n * Common driver utility functions.\n */\nexport class DriverUtils {\n    // -------------------------------------------------------------------------\n    // Public Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Returns true if given driver is SQLite-based driver.\n     */\n    static isSQLiteFamily(driver: Driver): boolean {\n        return [\n            \"sqlite\",\n            \"cordova\",\n            \"react-native\",\n            \"nativescript\",\n            \"sqljs\",\n            \"expo\",\n            \"better-sqlite3\",\n            \"capacitor\",\n        ].includes(driver.options.type)\n    }\n\n    /**\n     * Returns true if given driver is MySQL-based driver.\n     */\n    static isMySQLFamily(driver: Driver): boolean {\n        return [\"mysql\", \"mariadb\"].includes(driver.options.type)\n    }\n\n    static isReleaseVersionOrGreater(driver: Driver, version: string): boolean {\n        return VersionUtils.isGreaterOrEqual(driver.version, version)\n    }\n\n    static isPostgresFamily(driver: Driver): boolean {\n        return [\"postgres\", \"aurora-postgres\", \"cockroachdb\"].includes(\n            driver.options.type,\n        )\n    }\n\n    /**\n     * Normalizes and builds a new driver options.\n     * Extracts settings from connection url and sets to a new options object.\n     */\n    static buildDriverOptions(\n        options: any,\n        buildOptions?: { useSid: boolean },\n    ): any {\n        if (options.url) {\n            const urlDriverOptions = this.parseConnectionUrl(options.url) as {\n                [key: string]: any\n            }\n\n            if (\n                buildOptions &&\n                buildOptions.useSid &&\n                urlDriverOptions.database\n            ) {\n                urlDriverOptions.sid = urlDriverOptions.database\n            }\n\n            for (const key of Object.keys(urlDriverOptions)) {\n                if (typeof urlDriverOptions[key] === \"undefined\") {\n                    delete urlDriverOptions[key]\n                }\n            }\n\n            return Object.assign({}, options, urlDriverOptions)\n        }\n        return Object.assign({}, options)\n    }\n\n    /**\n     * buildDriverOptions for MongodDB only to support replica set\n     */\n    static buildMongoDBDriverOptions(\n        options: any,\n        buildOptions?: { useSid: boolean },\n    ): any {\n        if (options.url) {\n            const urlDriverOptions = this.parseMongoDBConnectionUrl(\n                options.url,\n            ) as { [key: string]: any }\n\n            if (\n                buildOptions &&\n                buildOptions.useSid &&\n                urlDriverOptions.database\n            ) {\n                urlDriverOptions.sid = urlDriverOptions.database\n            }\n\n            for (const key of Object.keys(urlDriverOptions)) {\n                if (typeof urlDriverOptions[key] === \"undefined\") {\n                    delete urlDriverOptions[key]\n                }\n            }\n\n            return Object.assign({}, options, urlDriverOptions)\n        }\n        return Object.assign({}, options)\n    }\n\n    /**\n     * Joins and shortens alias if needed.\n     *\n     * If the alias length is greater than the limit allowed by the current\n     * driver, replaces it with a shortend string, if the shortend string\n     * is still too long, it will then hash the alias.\n     *\n     * @param driver Current `Driver`.\n     * @param buildOptions Optional settings.\n     * @param alias Alias parts.\n     *\n     * @return An alias that is no longer than the divers max alias length.\n     */\n    static buildAlias(\n        { maxAliasLength }: Driver,\n        buildOptions: { shorten?: boolean; joiner?: string } | undefined,\n        ...alias: string[]\n    ): string {\n        const joiner =\n            buildOptions && buildOptions.joiner ? buildOptions.joiner : \"_\"\n\n        const newAlias = alias.length === 1 ? alias[0] : alias.join(joiner)\n\n        if (\n            maxAliasLength &&\n            maxAliasLength > 0 &&\n            newAlias.length > maxAliasLength\n        ) {\n            if (buildOptions && buildOptions.shorten === true) {\n                const shortenedAlias = shorten(newAlias)\n                if (shortenedAlias.length < maxAliasLength) {\n                    return shortenedAlias\n                }\n            }\n\n            return hash(newAlias, { length: maxAliasLength })\n        }\n\n        return newAlias\n    }\n\n    /**\n     * @deprecated use `buildAlias` instead.\n     */\n    static buildColumnAlias(\n        { maxAliasLength }: Driver,\n        buildOptions: { shorten?: boolean; joiner?: string } | string,\n        ...alias: string[]\n    ) {\n        if (typeof buildOptions === \"string\") {\n            alias.unshift(buildOptions)\n            buildOptions = { shorten: false, joiner: \"_\" }\n        } else {\n            buildOptions = Object.assign(\n                { shorten: false, joiner: \"_\" },\n                buildOptions,\n            )\n        }\n        return this.buildAlias(\n            { maxAliasLength } as Driver,\n            buildOptions,\n            ...alias,\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Private Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Extracts connection data from the connection url.\n     */\n    private static parseConnectionUrl(url: string) {\n        const type = url.split(\":\")[0]\n        const firstSlashes = url.indexOf(\"//\")\n        const preBase = url.substr(firstSlashes + 2)\n        const secondSlash = preBase.indexOf(\"/\")\n        const base =\n            secondSlash !== -1 ? preBase.substr(0, secondSlash) : preBase\n        let afterBase =\n            secondSlash !== -1 ? preBase.substr(secondSlash + 1) : undefined\n        // remove mongodb query params\n        if (afterBase && afterBase.indexOf(\"?\") !== -1) {\n            afterBase = afterBase.substr(0, afterBase.indexOf(\"?\"))\n        }\n\n        const lastAtSign = base.lastIndexOf(\"@\")\n        const usernameAndPassword = base.substr(0, lastAtSign)\n        const hostAndPort = base.substr(lastAtSign + 1)\n\n        let username = usernameAndPassword\n        let password = \"\"\n        const firstColon = usernameAndPassword.indexOf(\":\")\n        if (firstColon !== -1) {\n            username = usernameAndPassword.substr(0, firstColon)\n            password = usernameAndPassword.substr(firstColon + 1)\n        }\n        const [host, port] = hostAndPort.split(\":\")\n\n        return {\n            type: type,\n            host: host,\n            username: decodeURIComponent(username),\n            password: decodeURIComponent(password),\n            port: port ? parseInt(port) : undefined,\n            database: afterBase || undefined,\n        }\n    }\n\n    /**\n     * Extracts connection data from the connection url for MongoDB to support replica set.\n     */\n    private static parseMongoDBConnectionUrl(url: string) {\n        const type = url.split(\":\")[0]\n        const firstSlashes = url.indexOf(\"//\")\n        const preBase = url.substr(firstSlashes + 2)\n        const secondSlash = preBase.indexOf(\"/\")\n        const base =\n            secondSlash !== -1 ? preBase.substr(0, secondSlash) : preBase\n        let afterBase =\n            secondSlash !== -1 ? preBase.substr(secondSlash + 1) : undefined\n        let afterQuestionMark = \"\"\n        let host = undefined\n        let port = undefined\n        let hostReplicaSet = undefined\n        let replicaSet = undefined\n\n        const optionsObject: any = {}\n\n        if (afterBase && afterBase.indexOf(\"?\") !== -1) {\n            // split params\n            afterQuestionMark = afterBase.substr(\n                afterBase.indexOf(\"?\") + 1,\n                afterBase.length,\n            )\n\n            const optionsList = afterQuestionMark.split(\"&\")\n            let optionKey: string\n            let optionValue: string\n\n            // create optionsObject for merge with connectionUrl object before return\n            optionsList.forEach((optionItem) => {\n                optionKey = optionItem.split(\"=\")[0]\n                optionValue = optionItem.split(\"=\")[1]\n                optionsObject[optionKey] = optionValue\n            })\n\n            // specific replicaSet value to set options about hostReplicaSet\n            replicaSet = optionsObject[\"replicaSet\"]\n            afterBase = afterBase.substr(0, afterBase.indexOf(\"?\"))\n        }\n\n        const lastAtSign = base.lastIndexOf(\"@\")\n        const usernameAndPassword = base.substr(0, lastAtSign)\n        const hostAndPort = base.substr(lastAtSign + 1)\n\n        let username = usernameAndPassword\n        let password = \"\"\n        const firstColon = usernameAndPassword.indexOf(\":\")\n        if (firstColon !== -1) {\n            username = usernameAndPassword.substr(0, firstColon)\n            password = usernameAndPassword.substr(firstColon + 1)\n        }\n\n        // If replicaSet have value set It as hostlist, If not set like standalone host\n        if (replicaSet) {\n            hostReplicaSet = hostAndPort\n        } else {\n            ;[host, port] = hostAndPort.split(\":\")\n        }\n\n        const connectionUrl: any = {\n            type: type,\n            host: host,\n            hostReplicaSet: hostReplicaSet,\n            username: decodeURIComponent(username),\n            password: decodeURIComponent(password),\n            port: port ? parseInt(port) : undefined,\n            database: afterBase || undefined,\n        }\n\n        // Loop to set every options in connectionUrl to object\n        for (const [key, value] of Object.entries(optionsObject)) {\n            connectionUrl[key] = value\n        }\n\n        return connectionUrl\n    }\n}\n"], "sourceRoot": ".."}