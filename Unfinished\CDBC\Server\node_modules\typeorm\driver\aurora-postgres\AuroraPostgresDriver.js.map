{"version": 3, "sources": ["../../src/driver/aurora-postgres/AuroraPostgresDriver.ts"], "names": [], "mappings": ";;;AACA,+DAA2D;AAC3D,gEAA4D;AAG5D,2EAAuE;AAGvE,8EAA0E;AAC1E,gDAA4C;AAE5C,MAAe,eAAgB,SAAQ,+BAAc;CAIpD;AAED,MAAa,oBAAqB,SAAQ,eAAe;IAoCrD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,EAAE,CAAA;QAxBX;;WAEG;QACH,uBAAkB,GAAG,QAAiB,CAAA;QAsBlC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAA0C,CAAA;QACpE,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;QAEzB,wBAAwB;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,aAAa,CAChC,IAAI,CAAC,OAAO,CAAC,MAAM,EACnB,IAAI,CAAC,OAAO,CAAC,SAAS,EACtB,IAAI,CAAC,OAAO,CAAC,WAAW,EACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB,CAAC,KAAa,EAAE,UAAkB,EAAE,EAAE,CAClC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,EACtD,IAAI,CAAC,OAAO,CAAC,oBAAoB,EACjC,IAAI,CAAC,OAAO,CAAC,aAAa,CAC7B,CAAA;QAED,IAAI,CAAC,QAAQ,GAAG,yBAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAA;IACzE,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;;;OAIG;IACH,KAAK,CAAC,OAAO,KAAmB,CAAC;IAEjC;;OAEG;IACH,KAAK,CAAC,UAAU,KAAmB,CAAC;IAEpC;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,qDAAyB,CAChC,IAAI,EACJ,IAAI,IAAI,CAAC,aAAa,CAClB,IAAI,CAAC,OAAO,CAAC,MAAM,EACnB,IAAI,CAAC,OAAO,CAAC,SAAS,EACtB,IAAI,CAAC,OAAO,CAAC,WAAW,EACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB,CAAC,KAAa,EAAE,UAAkB,EAAE,EAAE,CAClC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,EACtD,IAAI,CAAC,OAAO,CAAC,oBAAoB,EACjC,IAAI,CAAC,OAAO,CAAC,aAAa,CAC7B,EACD,IAAI,CACP,CAAA;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IACI,IAAI,CAAC,OAAO,CAAC,aAAa;YAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,KAAK,KAAK,EACrD,CAAC;YACC,OAAO,KAAK,CAAC,sBAAsB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAC9D,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IACI,IAAI,CAAC,OAAO,CAAC,aAAa;YAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,KAAK,KAAK,EACrD,CAAC;YACC,OAAO,KAAK,CAAC,oBAAoB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAC5D,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;IAClE,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,MAAM,MAAM,GACR,IAAI,CAAC,OAAO,CAAC,MAAM;YACnB,6BAAa,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QACxD,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,CAAA;QAErB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,UAAe,EAAE,KAAa;QACjD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAA;QAElE,IAAI,kBAAkB,CAAC,aAAa,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;QACpE,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;CACJ;AA9KD,oDA8KC", "file": "AuroraPostgresDriver.js", "sourcesContent": ["import { Driver } from \"../Driver\"\nimport { PostgresDriver } from \"../postgres/PostgresDriver\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { AuroraPostgresConnectionOptions } from \"./AuroraPostgresConnectionOptions\"\nimport { AuroraPostgresQueryRunner } from \"./AuroraPostgresQueryRunner\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { DriverUtils } from \"../DriverUtils\"\n\nabstract class PostgresWrapper extends PostgresDriver {\n    options: any\n\n    abstract createQueryRunner(mode: ReplicationMode): any\n}\n\nexport class AuroraPostgresDriver extends PostgresWrapper implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * Aurora Data API underlying library.\n     */\n    DataApiDriver: any\n\n    client: any\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"nested\" as const\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: AuroraPostgresConnectionOptions\n\n    /**\n     * Master database used to perform all write queries.\n     */\n    database?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super()\n        this.connection = connection\n        this.options = connection.options as AuroraPostgresConnectionOptions\n        this.isReplicated = false\n\n        // load data-api package\n        this.loadDependencies()\n\n        this.client = new this.DataApiDriver(\n            this.options.region,\n            this.options.secretArn,\n            this.options.resourceArn,\n            this.options.database,\n            (query: string, parameters?: any[]) =>\n                this.connection.logger.logQuery(query, parameters),\n            this.options.serviceConfigOptions,\n            this.options.formatOptions,\n        )\n\n        this.database = DriverUtils.buildDriverOptions(this.options).database\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     * Based on pooling options, it can either create connection immediately,\n     * either create a pool and create connection when needed.\n     */\n    async connect(): Promise<void> {}\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {}\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return new AuroraPostgresQueryRunner(\n            this,\n            new this.DataApiDriver(\n                this.options.region,\n                this.options.secretArn,\n                this.options.resourceArn,\n                this.options.database,\n                (query: string, parameters?: any[]) =>\n                    this.connection.logger.logQuery(query, parameters),\n                this.options.serviceConfigOptions,\n                this.options.formatOptions,\n            ),\n            mode,\n        )\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (\n            this.options.formatOptions &&\n            this.options.formatOptions.castParameters === false\n        ) {\n            return super.preparePersistentValue(value, columnMetadata)\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return this.client.preparePersistentValue(value, columnMetadata)\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (\n            this.options.formatOptions &&\n            this.options.formatOptions.castParameters === false\n        ) {\n            return super.prepareHydratedValue(value, columnMetadata)\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return this.client.prepareHydratedValue(value, columnMetadata)\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        const driver =\n            this.options.driver ||\n            PlatformTools.load(\"typeorm-aurora-data-api-driver\")\n        const { pg } = driver\n\n        this.DataApiDriver = pg\n    }\n\n    /**\n     * Executes given query.\n     */\n    protected executeQuery(connection: any, query: string) {\n        return this.connection.query(query)\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    async afterConnect(): Promise<void> {\n        const extensionsMetadata = await this.checkMetadataForExtensions()\n\n        if (extensionsMetadata.hasExtensions) {\n            await this.enableExtensions(extensionsMetadata, this.connection)\n        }\n\n        return Promise.resolve()\n    }\n}\n"], "sourceRoot": "../.."}