{"version": 3, "sources": ["../../src/driver/mysql/MysqlDriver.ts"], "names": [], "mappings": ";;;AACA,iFAA6E;AAC7E,+FAA2F;AAC3F,gDAA4C;AAE5C,yDAAqD;AAGrD,oDAAgD;AAChD,gEAA4D;AAE5D,gFAA4E;AAQ5E,kDAA8C;AAC9C,8EAA0E;AAE1E,uCAA0C;AAI1C,0DAAsD;AACtD,gEAA4D;AAG5D;;GAEG;AACH,MAAa,WAAW;IAmTpB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QA1QlC;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAG,QAAiB,CAAA;QAEtC;;;;;WAKG;QACH,uBAAkB,GAAiB;YAC/B,gBAAgB;YAChB,KAAK;YACL,KAAK;YACL,SAAS,EAAE,kBAAkB;YAC7B,SAAS;YACT,UAAU;YACV,WAAW;YACX,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,kBAAkB,EAAE,qBAAqB;YACzC,MAAM,EAAE,qBAAqB;YAC7B,SAAS;YACT,KAAK,EAAE,sBAAsB;YAC7B,SAAS,EAAE,sBAAsB;YACjC,OAAO,EAAE,sBAAsB;YAC/B,MAAM,EAAE,sBAAsB;YAC9B,SAAS,EAAE,sBAAsB;YACjC,sBAAsB;YACtB,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,MAAM;YACN,eAAe;YACf,MAAM;YACN,OAAO,EAAE,4BAA4B;YACrC,eAAe;YACf,SAAS;YACT,UAAU,EAAE,+BAA+B;YAC3C,kBAAkB;YAClB,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,UAAU;YACV,MAAM;YACN,KAAK;YACL,QAAQ;YACR,WAAW;YACX,iBAAiB;YACjB,MAAM;YACN,qBAAqB;YACrB,UAAU;YACV,OAAO;YACP,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,iBAAiB;YACjB,cAAc;YACd,oBAAoB;YACpB,oCAAoC;YACpC,MAAM;YACN,OAAO;YACP,OAAO;SACV,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,yBAAyB,CAAC,CAAA;QAEhE;;WAEG;QACH,iBAAY,GAAiB;YACzB,UAAU;YACV,OAAO;YACP,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,iBAAiB;YACjB,cAAc;YACd,oBAAoB;SACvB,CAAA;QAED;;WAEG;QACH,0BAAqB,GAAiB;YAClC,MAAM;YACN,SAAS;YACT,UAAU;YACV,QAAQ;YACR,WAAW;SACd,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB;YACjC,KAAK;YACL,SAAS;YACT,UAAU;YACV,WAAW;YACX,KAAK;YACL,SAAS;YACT,QAAQ;SACX,CAAA;QAED;;WAEG;QACH,6BAAwB,GAAiB;YACrC,SAAS;YACT,KAAK;YACL,SAAS;YACT,OAAO;YACP,OAAO;YACP,QAAQ;YACR,kBAAkB;YAClB,MAAM;YACN,MAAM;YACN,UAAU;YACV,WAAW;SACd,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB;YACjC,SAAS;YACT,KAAK;YACL,SAAS;YACT,OAAO;YACP,OAAO;YACP,QAAQ;YACR,kBAAkB;YAClB,MAAM;SACT,CAAA;QAED;;WAEG;QACH,6BAAwB,GAAiB;YACrC,KAAK;YACL,SAAS;YACT,UAAU;YACV,SAAS;YACT,WAAW;YACX,QAAQ;YACR,SAAS;YACT,KAAK;YACL,SAAS;YACT,OAAO;YACP,OAAO;YACP,QAAQ;YACR,kBAAkB;YAClB,MAAM;SACT,CAAA;QAED;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,UAAU;YACtB,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,sBAAsB;YACzC,UAAU,EAAE,UAAU;YACtB,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,sBAAsB;YACzC,UAAU,EAAE,UAAU;YACtB,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,SAAS;YACxB,kBAAkB,EAAE,QAAQ;YAC5B,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE,KAAK;YACpB,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,MAAM;YACnB,YAAY,EAAE,SAAS;YACvB,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,SAAS;YACzB,aAAa,EAAE,SAAS;YACxB,YAAY,EAAE,SAAS;YACvB,aAAa,EAAE,MAAM;SACxB,CAAA;QAED;;;WAGG;QACH,qBAAgB,GAAqB;YACjC,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACzB,kBAAkB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACnC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACnB,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACrB,SAAS,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YAC1B,OAAO,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACpC,GAAG,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YAChC,OAAO,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACpC,KAAK,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YAClC,KAAK,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;YACxB,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;YACzB,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YACtB,QAAQ,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC1B,SAAS,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC3B,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACjB,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;YAClB,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;YACtB,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACrB,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACtB,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACvB,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;SACxB,CAAA;QAED;;;WAGG;QACH,mBAAc,GAAG,EAAE,CAAA;QAEnB,oBAAe,GAAoB;YAC/B,OAAO,EAAE,KAAK;YACd,qBAAqB,EAAE,IAAI;SAC9B,CAAA;QAED;;WAEG;QACc,6BAAwB,GACrC;YACI,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;SAChB,CAAA;QAEL,2DAA2D;QACnD,2BAAsB,GAAG,KAAK,CAAA;QAOlC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG;YACX,oBAAoB,EAAE,IAAI;YAC1B,GAAG,UAAU,CAAC,OAAO;SACE,CAAA;QAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAE3D,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,QAAQ,GAAG,yBAAW,CAAC,kBAAkB,CAC1C,IAAI,CAAC,OAAO,CAAC,WAAW;YACpB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM;YACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CACrB,CAAC,QAAQ,CAAA;QAEV,kDAAkD;QAClD,oDAAoD;QACpD,iHAAiH;QACjH,gEAAgE;QAChE,8BAA8B;QAC9B,qDAAqD;QACrD,8BAA8B;QAC9B,qDAAqD;QACrD,gHAAgH;QAChH,2EAA2E;IAC/E,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAC3C,IAAI,CAAC,OAAO,CAAC,WAAW,CAC3B,CAAA;YACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACrD,IAAI,CAAC,WAAW,CAAC,GAAG,CAChB,OAAO,GAAG,KAAK,EACf,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CACpD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,WAAW,CAAC,GAAG,CAChB,QAAQ,EACR,IAAI,CAAC,uBAAuB,CACxB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAClC,CACJ,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAC7B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAC3D,CAAA;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;YAEpD,IAAI,CAAC,QAAQ,GAAG,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAA;YAEtD,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC/B,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;QACpD,IAAI,CAAC,OAAO,GAAG,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAC7C,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAE3B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,2BAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,IAAI,CAAA;YAC/C,CAAC;YACD,IAAI,2BAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,IAAI,CAAA;YAC/C,CAAC;YACD,IAAI,2BAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAA;YACvC,CAAC;YACD,IAAI,2BAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAA;YACtC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACvC,IAAI,2BAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAA;YACvC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAClC,MAAM,IAAI,iDAAuB,CAAC,OAAO,CAAC,CAAA;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;gBAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;gBAC5D,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;YAChC,CAAC,CAAC,CAAA;QACN,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;gBAClC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE;oBACvB,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;oBACrB,EAAE,EAAE,CAAA;gBACR,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,uCAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,mCAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,CACjC,CAAA;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QACpE,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,GAAG,GAAG,UAAU,GAAG,GAAG,CAAA;IACjC,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,SAAiB,EACjB,MAAe,EACf,QAAiB;QAEjB,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7B,IAAI,QAAQ,EAAE,CAAC;YACX,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAC/B,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAG,SAAS,CAAA;QAE9B,IAAI,iCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAE/C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,OAAO;YACH,QAAQ,EACJ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,cAAc;YAC/D,MAAM,EAAE,YAAY;YACpB,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC3C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,OAAO,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,MAAM;YAC9B,cAAc,CAAC,IAAI,KAAK,aAAa,EACvC,CAAC;YACC,OAAO,EAAE,GAAG,KAAK,CAAA;QACrB,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YACvC,OAAO,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,+CAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,IACI,cAAc,CAAC,IAAI,KAAK,OAAO;YAC/B,cAAc,CAAC,IAAI,KAAK,MAAM;YAC9B,cAAc,CAAC,IAAI,KAAK,SAAS,EACnC,CAAC;YACC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACjE,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,KAAK,GAAG,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IACH,CAAC,cAAc,CAAC,IAAI,KAAK,MAAM;YAC3B,cAAc,CAAC,IAAI,KAAK,aAAa,CAAC;YAC1C,cAAc,CAAC,IAAI;YACnB,CAAC,KAAK,CAAC,KAAK,CAAC;YACb,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EACnD,CAAC;YACC,4DAA4D;YAC5D,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YACvC,KAAK,GAAG,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAKb;QACG,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACtD,OAAO,KAAK,CAAA;QAChB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChE,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,MAAM;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;YAC/B,CAAC,2BAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EACxD,CAAC;YACC;;;;;;eAMG;YACH,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,cAAc;YAC9B,MAAM,CAAC,IAAI,KAAK,aAAa,EAC/B,CAAC;YACC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,kBAAkB;YAClC,MAAM,CAAC,IAAI,KAAK,MAAM,EACxB,CAAC;YACC,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,KAAK;YACrB,MAAM,CAAC,IAAI,KAAK,SAAS;YACzB,MAAM,CAAC,IAAI,KAAK,OAAO,EACzB,CAAC;YACC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7D,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,UAAU;YAC1B,MAAM,CAAC,IAAI,KAAK,kBAAkB,EACpC,CAAC;YACC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACpE,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAA;QAE3C,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;YACxB,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,IACI,CAAC,cAAc,CAAC,IAAI,KAAK,MAAM;YAC3B,cAAc,CAAC,IAAI,KAAK,aAAa;YACrC,OAAO,YAAY,KAAK,QAAQ,CAAC;YACrC,YAAY,KAAK,SAAS,EAC5B,CAAC;YACC,OAAO,IAAI,YAAY,GAAG,CAAA;QAC9B,CAAC;QAED,IAAI,cAAc,CAAC,IAAI,KAAK,KAAK,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC9D,OAAO,IAAI,qBAAS,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAG,CAAA;QAC7D,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAA;QAC5D,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,YAAY,EAAE,CAAA;YAC5B,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;QAED,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC7B,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,OAAO,GAAG,YAAY,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,QAAQ;YACZ,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;YACxB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAChC,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAoC;QAChD,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;QAElD;;;WAGG;QACH,IACI,MAAM,CAAC,kBAAkB,KAAK,MAAM;YACpC,CAAC,IAAI,CAAC,sBAAsB;YAE5B,OAAO,IAAI,CAAA;QAEf,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,kBAAkB;gBACnB,OAAO,KAAK,CAAA;YAChB,KAAK,WAAW;gBACZ,OAAO,KAAK,CAAA;YAChB;gBACI,OAAO,EAAE,CAAA;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAEtB,6HAA6H;QAC7H,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAA;QAC/C,CAAC;aAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,IAAI,IAAI,IAAI,MAAM,CAAC,KAAK,GAAG,CAAA;QAC/B,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,GAAG,CAAA;QACnD,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,IAAI,QAAQ,CAAA;QAEpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,sBAAsB;QAClB,OAAO,IAAI,OAAO,CAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACjC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,CAAC,aAAa,CAC1B,QAAQ,EACR,CAAC,GAAQ,EAAE,YAAiB,EAAE,EAAE;oBAC5B,GAAG;wBACC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;wBACX,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAA;gBACpD,CAAC,CACJ,CAAA;YACL,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAQ,EAAE,YAAiB,EAAE,EAAE;oBACpD,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAA;gBAChE,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,IAAI,CACA,IAAI,oBAAY,CACZ,mDAAmD,CACtD,CACJ,CAAA;YACL,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACjB,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAE3D,OAAO,IAAI,OAAO,CAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACjC,IAAI,CAAC,WAAW,CAAC,aAAa,CAC1B,QAAQ,EACR,CAAC,GAAQ,EAAE,YAAiB,EAAE,EAAE;gBAC5B,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAA;YAChE,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,QAAwB,EACxB,YAAiB,EACjB,WAAmB;QAEnB,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,IAAI,YAAY,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAA;gBACvD,IAAI,MAAM,EAAE,CAAC;oBACT,mBAAQ,CAAC,SAAS,CACd,GAAG,EACH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAC3C,CAAA;oBACD,8KAA8K;gBAClL,CAAC;gBACD,OAAO,GAAG,CAAA;YACd,CAAC,EAAE,EAAmB,CAAC,CAAA;QAC3B,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,eAAe,EAAE,EAAE;YACrB,IAAI,KAAU,CAAA;YACd,IACI,eAAe,CAAC,kBAAkB,KAAK,WAAW;gBAClD,YAAY,CAAC,QAAQ,EACvB,CAAC;gBACC,qEAAqE;gBACrE,qEAAqE;gBACrE,KAAK,GAAG,YAAY,CAAC,QAAQ,GAAG,WAAW,CAAA;gBAC3C,8DAA8D;gBAC9D,sEAAsE;gBACtE,uDAAuD;YAC3D,CAAC;YAED,OAAO,mBAAQ,CAAC,SAAS,CACrB,GAAG,EACH,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CACxC,CAAA;QACL,CAAC,EACD,EAAmB,CACtB,CAAA;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAA;IAC1E,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAA,CAAC,4DAA4D;YAE3F,MAAM,eAAe,GACjB,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,cAAc,CAAC;gBACzD,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBAC3D,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK;gBAC1C,CAAC,cAAc,CAAC,SAAS,KAAK,SAAS;oBACnC,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS,CAAC;gBACvD,CAAC,cAAc,CAAC,KAAK,KAAK,SAAS;oBAC/B,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,CAAC;gBAC/C,WAAW,CAAC,QAAQ,KAAK,cAAc,CAAC,QAAQ;gBAChD,WAAW,CAAC,QAAQ,KAAK,cAAc,CAAC,QAAQ;gBAChD,WAAW,CAAC,YAAY,KAAK,cAAc,CAAC,YAAY;gBACxD,WAAW,CAAC,aAAa,KAAK,cAAc,CAAC,aAAa;gBAC1D,WAAW,CAAC,OAAO;oBACf,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC9C,CAAC,IAAI,CAAC,oBAAoB,CACtB,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EACrC,WAAW,CAAC,OAAO,CACtB;gBACD,CAAC,WAAW,CAAC,IAAI;oBACb,cAAc,CAAC,IAAI;oBACnB,CAAC,mBAAQ,CAAC,aAAa,CACnB,WAAW,CAAC,IAAI,EAChB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAC7C,CAAC;gBACN,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,QAAQ,CAAC;gBAC3D,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,WAAW,CAAC;gBACxD,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC1C,CAAC,cAAc,CAAC,kBAAkB,KAAK,MAAM;oBACzC,WAAW,CAAC,WAAW,KAAK,cAAc,CAAC,WAAW,CAAC,CAAA;YAE/D,gBAAgB;YAChB,yBAAyB;YACzB,qEAAqE;YACrE,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,8CAA8C;YAC9C,QAAQ;YACR,mBAAmB;YACnB,qBAAqB;YACrB,8BAA8B;YAC9B,iCAAiC;YACjC,QAAQ;YACR,qEAAqE;YACrE,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,qEAAqE;YACrE,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,mCAAmC;YACnC,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,mCAAmC;YACnC,QAAQ;YACR,mBAAmB;YACnB,2BAA2B;YAC3B,oCAAoC;YACpC,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,qCAAqC;YACrC,wCAAwC;YACxC,QAAQ;YACR,mBAAmB;YACnB,sBAAsB;YACtB,+BAA+B;YAC/B,sDAAsD;YACtD,QAAQ;YACR,mBAAmB;YACnB,sBAAsB;YACtB,+BAA+B;YAC/B,iDAAiD;YACjD,QAAQ;YACR,kEAAkE;YAClE,mBAAmB;YACnB,8BAA8B;YAC9B,sCAAsC;YACtC,qDAAqD;YACrD,mCAAmC;YACnC,aAAa;YACb,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,mBAAmB;YACnB,iCAAiC;YACjC,oEAAoE;YACpE,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,kDAAkD;YAClD,QAAQ;YACR,mBAAmB;YACnB,0BAA0B;YAC1B,mCAAmC;YACnC,sCAAsC;YACtC,QAAQ;YACR,mBAAmB;YACnB,0DAA0D;YAC1D,sEAAsE;YACtE,QAAQ;YACR,gEAAgE;YAChE,IAAI;YAEJ,OAAO,eAAe,CAAA;QAC1B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,aAA4B;QAChD,OAAO,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAA;IACvD,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAA;QACjE,MAAM,wBAAwB,GAC1B,gBAAgB,KAAK,OAAO;YACxB,CAAC,CAAE,QAAkB;YACrB,CAAC,CAAE,OAAiB,CAAA;QAC5B,IAAI,CAAC;YACD,sCAAsC;YACtC,MAAM,KAAK,GACP,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,6BAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC/D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;YAClB;;;;;;eAMG;YACH,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,oBAAY,CAClB,IAAI,gBAAgB,iDAAiD,wBAAwB,IAAI,CACpG,CAAA;YACL,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,IAAI,CAAC;gBACD,IAAI,CAAC,KAAK,GAAG,6BAAa,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA,CAAC,uCAAuC;YACrG,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,IAAI,+DAA8B,CACpC,OAAO,EACP,gBAAgB,CACnB,CAAA;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,uBAAuB,CAC7B,OAA+B,EAC/B,WAA8C;QAE9C,WAAW,GAAG,MAAM,CAAC,MAAM,CACvB,EAAE,EACF,WAAW,EACX,yBAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAC9C,CAAA,CAAC,yBAAyB;QAE3B,0CAA0C;QAC1C,OAAO,MAAM,CAAC,MAAM,CAChB,EAAE,EACF;YACI,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,iBAAiB,EACb,OAAO,CAAC,iBAAiB,KAAK,SAAS;gBACnC,CAAC,CAAC,OAAO,CAAC,iBAAiB;gBAC3B,CAAC,CAAC,IAAI;YACd,gBAAgB,EACZ,OAAO,CAAC,gBAAgB,KAAK,SAAS;gBAClC,CAAC,CAAC,OAAO,CAAC,gBAAgB;gBAC1B,CAAC,CAAC,IAAI;YACd,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,KAAK,EAAE,OAAO,CAAC,KAAK;SACvB,EACD;YACI,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,IAAI,EAAE,WAAW,CAAC,QAAQ;YAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,UAAU,EAAE,WAAW,CAAC,UAAU;SACrC,EACD,OAAO,CAAC,cAAc,KAAK,SAAS;YAChC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,EAAE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,EAChD,EAAE,eAAe,EAAE,OAAO,CAAC,QAAQ,EAAE,EACrC,OAAO,CAAC,KAAK,IAAI,EAAE,CACtB,CAAA;IACL,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,iBAAsB;QACvC,2BAA2B;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAA;QAErD,uCAAuC;QACvC,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,qGAAqG;YACrG,iFAAiF;YACjF,IAAI,CAAC,aAAa,CAAC,CAAC,GAAQ,EAAE,UAAe,EAAE,EAAE;gBAC7C,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;gBAEzC,UAAU,CAAC,OAAO,EAAE,CAAA;gBACpB,EAAE,CAAC,IAAI,CAAC,CAAA;YACZ,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,UAAe;QACvC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAClC;;;WAGG;QACH,IAAI,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7C,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE,CAClC,MAAM,CAAC,GAAG,CACN,MAAM,EACN,qCAAqC,KAAK,EAAE,CAC/C,CACJ,CAAA;QACL,CAAC;QACD,OAAO,UAAU,CAAA;IACrB,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,mBAAuC,EACvC,aAAiC;QAEjC,IACI,OAAO,mBAAmB,KAAK,QAAQ;YACvC,OAAO,aAAa,KAAK,QAAQ,EACnC,CAAC;YACC,qGAAqG;YACrG,0EAA0E;YAC1E,mBAAmB,GAAG,mBAAmB,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;YACjE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;QACzD,CAAC;QAED,OAAO,mBAAmB,KAAK,aAAa,CAAA;IAChD,CAAC;IAED,qBAAqB,CACjB,cAA8B,EAC9B,WAAwB;QAExB,2EAA2E;QAC3E,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAA;QACjD,IAAI,SAAS,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAA;QACf,CAAC;QAED,OAAO,cAAc,CAAC,UAAU,KAAK,WAAW,CAAC,UAAU,CAAA;IAC/D,CAAC;IAED;;;OAGG;IACO,yBAAyB,CAAC,KAAc;QAC9C,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAA;QAExB,sCAAsC;QACtC,MAAM,kBAAkB,GACpB,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACvD,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;QAE7C,IAAI,kBAAkB,EAAE,CAAC;YACrB,gCAAgC;YAChC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACxC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAClC,OAAO,SAAS;oBACZ,CAAC,CAAC,oBAAoB,SAAS,CAAC,CAAC,CAAC,EAAE;oBACpC,CAAC,CAAC,qBAAqB,CAAA;YAC/B,CAAC;iBAAM,CAAC;gBACJ,OAAO,SAAS;oBACZ,CAAC,CAAC,oBAAoB,SAAS,CAAC,CAAC,CAAC,EAAE;oBACpC,CAAC,CAAC,mBAAmB,CAAA;YAC7B,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAA;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAA;QAE5B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAEjF,OAAO,OAAO,CAAA;IAClB,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAC3B,WAAwB,EACxB,cAA8B;QAE9B,gFAAgF;QAChF,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,MAAM;YAC7C,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,UAAU;YAE7C,OAAO,KAAK,CAAA;QAChB,OAAO,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAA;IAClE,CAAC;CACJ;AAt0CD,kCAs0CC", "file": "MysqlDriver.js", "sourcesContent": ["import { Driver, ReturningType } from \"../Driver\"\nimport { ConnectionIsNotSetError } from \"../../error/ConnectionIsNotSetError\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { MysqlQueryRunner } from \"./MysqlQueryRunner\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { MysqlConnectionOptions } from \"./MysqlConnectionOptions\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { MysqlConnectionCredentialsOptions } from \"./MysqlConnectionCredentialsOptions\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { TypeORMError } from \"../../error\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { VersionUtils } from \"../../util/VersionUtils\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { UpsertType } from \"../types/UpsertType\"\n\n/**\n * Organizes communication with MySQL DBMS.\n */\nexport class MysqlDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * Mysql underlying library.\n     */\n    mysql: any\n\n    /**\n     * Connection pool.\n     * Used in non-replication mode.\n     */\n    pool: any\n\n    /**\n     * Pool cluster used in replication mode.\n     */\n    poolCluster: any\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: MysqlConnectionOptions\n\n    /**\n     * Version of MySQL. Requires a SQL query to the DB, so it is not always set\n     */\n    version?: string\n\n    /**\n     * Master database used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"nested\" as const\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://www.tutorialspoint.com/mysql/mysql-data-types.htm\n     * @see https://dev.mysql.com/doc/refman/8.0/en/data-types.html\n     */\n    supportedDataTypes: ColumnType[] = [\n        // numeric types\n        \"bit\",\n        \"int\",\n        \"integer\", // synonym for int\n        \"tinyint\",\n        \"smallint\",\n        \"mediumint\",\n        \"bigint\",\n        \"float\",\n        \"double\",\n        \"double precision\", // synonym for double\n        \"real\", // synonym for double\n        \"decimal\",\n        \"dec\", // synonym for decimal\n        \"numeric\", // synonym for decimal\n        \"fixed\", // synonym for decimal\n        \"bool\", // synonym for tinyint\n        \"boolean\", // synonym for tinyint\n        // date and time types\n        \"date\",\n        \"datetime\",\n        \"timestamp\",\n        \"time\",\n        \"year\",\n        // string types\n        \"char\",\n        \"nchar\", // synonym for national char\n        \"national char\",\n        \"varchar\",\n        \"nvarchar\", // synonym for national varchar\n        \"national varchar\",\n        \"blob\",\n        \"text\",\n        \"tinyblob\",\n        \"tinytext\",\n        \"mediumblob\",\n        \"mediumtext\",\n        \"longblob\",\n        \"longtext\",\n        \"enum\",\n        \"set\",\n        \"binary\",\n        \"varbinary\",\n        // json data type\n        \"json\",\n        // spatial data types\n        \"geometry\",\n        \"point\",\n        \"linestring\",\n        \"polygon\",\n        \"multipoint\",\n        \"multilinestring\",\n        \"multipolygon\",\n        \"geometrycollection\",\n        // additional data types for mariadb\n        \"uuid\",\n        \"inet4\",\n        \"inet6\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = [\"on-duplicate-key-update\"]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = [\n        \"geometry\",\n        \"point\",\n        \"linestring\",\n        \"polygon\",\n        \"multipoint\",\n        \"multilinestring\",\n        \"multipolygon\",\n        \"geometrycollection\",\n    ]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\n        \"char\",\n        \"varchar\",\n        \"nvarchar\",\n        \"binary\",\n        \"varbinary\",\n    ]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withWidthColumnTypes: ColumnType[] = [\n        \"bit\",\n        \"tinyint\",\n        \"smallint\",\n        \"mediumint\",\n        \"int\",\n        \"integer\",\n        \"bigint\",\n    ]\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = [\n        \"decimal\",\n        \"dec\",\n        \"numeric\",\n        \"fixed\",\n        \"float\",\n        \"double\",\n        \"double precision\",\n        \"real\",\n        \"time\",\n        \"datetime\",\n        \"timestamp\",\n    ]\n\n    /**\n     * Gets list of column data types that supports scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = [\n        \"decimal\",\n        \"dec\",\n        \"numeric\",\n        \"fixed\",\n        \"float\",\n        \"double\",\n        \"double precision\",\n        \"real\",\n    ]\n\n    /**\n     * Gets list of column data types that supports UNSIGNED and ZEROFILL attributes.\n     */\n    unsignedAndZerofillTypes: ColumnType[] = [\n        \"int\",\n        \"integer\",\n        \"smallint\",\n        \"tinyint\",\n        \"mediumint\",\n        \"bigint\",\n        \"decimal\",\n        \"dec\",\n        \"numeric\",\n        \"fixed\",\n        \"float\",\n        \"double\",\n        \"double precision\",\n        \"real\",\n    ]\n\n    /**\n     * ORM has special columns and we need to know what database column types should be for those columns.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"datetime\",\n        createDatePrecision: 6,\n        createDateDefault: \"CURRENT_TIMESTAMP(6)\",\n        updateDate: \"datetime\",\n        updateDatePrecision: 6,\n        updateDateDefault: \"CURRENT_TIMESTAMP(6)\",\n        deleteDate: \"datetime\",\n        deleteDatePrecision: 6,\n        deleteDateNullable: true,\n        version: \"int\",\n        treeLevel: \"int\",\n        migrationId: \"int\",\n        migrationName: \"varchar\",\n        migrationTimestamp: \"bigint\",\n        cacheId: \"int\",\n        cacheIdentifier: \"varchar\",\n        cacheTime: \"bigint\",\n        cacheDuration: \"int\",\n        cacheQuery: \"text\",\n        cacheResult: \"text\",\n        metadataType: \"varchar\",\n        metadataDatabase: \"varchar\",\n        metadataSchema: \"varchar\",\n        metadataTable: \"varchar\",\n        metadataName: \"varchar\",\n        metadataValue: \"text\",\n    }\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults = {\n        varchar: { length: 255 },\n        nvarchar: { length: 255 },\n        \"national varchar\": { length: 255 },\n        char: { length: 1 },\n        binary: { length: 1 },\n        varbinary: { length: 255 },\n        decimal: { precision: 10, scale: 0 },\n        dec: { precision: 10, scale: 0 },\n        numeric: { precision: 10, scale: 0 },\n        fixed: { precision: 10, scale: 0 },\n        float: { precision: 12 },\n        double: { precision: 22 },\n        time: { precision: 0 },\n        datetime: { precision: 0 },\n        timestamp: { precision: 0 },\n        bit: { width: 1 },\n        int: { width: 11 },\n        integer: { width: 11 },\n        tinyint: { width: 4 },\n        smallint: { width: 6 },\n        mediumint: { width: 9 },\n        bigint: { width: 20 },\n    }\n\n    /**\n     * Max length allowed by MySQL for aliases.\n     * @see https://dev.mysql.com/doc/refman/5.5/en/identifiers.html\n     */\n    maxAliasLength = 63\n\n    cteCapabilities: CteCapabilities = {\n        enabled: false,\n        requiresRecursiveHint: true,\n    }\n\n    /**\n     * Supported returning types\n     */\n    private readonly _isReturningSqlSupported: Record<ReturningType, boolean> =\n        {\n            delete: false,\n            insert: false,\n            update: false,\n        }\n\n    /** MariaDB supports uuid type for version 10.7.0 and up */\n    private uuidColumnTypeSuported = false\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        this.connection = connection\n        this.options = {\n            legacySpatialSupport: true,\n            ...connection.options,\n        } as MysqlConnectionOptions\n        this.isReplicated = this.options.replication ? true : false\n\n        // load mysql package\n        this.loadDependencies()\n\n        this.database = DriverUtils.buildDriverOptions(\n            this.options.replication\n                ? this.options.replication.master\n                : this.options,\n        ).database\n\n        // validate options to make sure everything is set\n        // todo: revisit validation with replication in mind\n        // if (!(this.options.host || (this.options.extra && this.options.extra.socketPath)) && !this.options.socketPath)\n        //     throw new DriverOptionNotSetError(\"socketPath and host\");\n        // if (!this.options.username)\n        //     throw new DriverOptionNotSetError(\"username\");\n        // if (!this.options.database)\n        //     throw new DriverOptionNotSetError(\"database\");\n        // todo: check what is going on when connection is setup without database and how to connect to a database then?\n        // todo: provide options to auto-create a database if it does not exist yet\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     */\n    async connect(): Promise<void> {\n        if (this.options.replication) {\n            this.poolCluster = this.mysql.createPoolCluster(\n                this.options.replication,\n            )\n            this.options.replication.slaves.forEach((slave, index) => {\n                this.poolCluster.add(\n                    \"SLAVE\" + index,\n                    this.createConnectionOptions(this.options, slave),\n                )\n            })\n            this.poolCluster.add(\n                \"MASTER\",\n                this.createConnectionOptions(\n                    this.options,\n                    this.options.replication.master,\n                ),\n            )\n        } else {\n            this.pool = await this.createPool(\n                this.createConnectionOptions(this.options, this.options),\n            )\n        }\n\n        if (!this.database) {\n            const queryRunner = this.createQueryRunner(\"master\")\n\n            this.database = await queryRunner.getCurrentDatabase()\n\n            await queryRunner.release()\n        }\n\n        const queryRunner = this.createQueryRunner(\"master\")\n        this.version = await queryRunner.getVersion()\n        await queryRunner.release()\n\n        if (this.options.type === \"mariadb\") {\n            if (VersionUtils.isGreaterOrEqual(this.version, \"10.0.5\")) {\n                this._isReturningSqlSupported.delete = true\n            }\n            if (VersionUtils.isGreaterOrEqual(this.version, \"10.5.0\")) {\n                this._isReturningSqlSupported.insert = true\n            }\n            if (VersionUtils.isGreaterOrEqual(this.version, \"10.2.0\")) {\n                this.cteCapabilities.enabled = true\n            }\n            if (VersionUtils.isGreaterOrEqual(this.version, \"10.7.0\")) {\n                this.uuidColumnTypeSuported = true\n            }\n        } else if (this.options.type === \"mysql\") {\n            if (VersionUtils.isGreaterOrEqual(this.version, \"8.0.0\")) {\n                this.cteCapabilities.enabled = true\n            }\n        }\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    afterConnect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with the database.\n     */\n    async disconnect(): Promise<void> {\n        if (!this.poolCluster && !this.pool) {\n            throw new ConnectionIsNotSetError(\"mysql\")\n        }\n\n        if (this.poolCluster) {\n            return new Promise<void>((ok, fail) => {\n                this.poolCluster.end((err: any) => (err ? fail(err) : ok()))\n                this.poolCluster = undefined\n            })\n        }\n        if (this.pool) {\n            return new Promise<void>((ok, fail) => {\n                this.pool.end((err: any) => {\n                    if (err) return fail(err)\n                    this.pool = undefined\n                    ok()\n                })\n            })\n        }\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return new MysqlQueryRunner(this, mode)\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => nativeParameters[key],\n        )\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                const value: any = parameters[key]\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (typeof value === \"function\") {\n                    return value()\n                }\n\n                escapedParameters.push(value)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return \"`\" + columnName + \"`\"\n    }\n\n    /**\n     * Build full table name with database name, schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(\n        tableName: string,\n        schema?: string,\n        database?: string,\n    ): string {\n        const tablePath = [tableName]\n\n        if (database) {\n            tablePath.unshift(database)\n        }\n\n        return tablePath.join(\".\")\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema = undefined\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            const parsed = this.parseTableName(target.name)\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isEntityMetadata(target)) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        return {\n            database:\n                (parts.length > 1 ? parts[0] : undefined) || driverDatabase,\n            schema: driverSchema,\n            tableName: parts.length > 1 ? parts[1] : parts[0],\n        }\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (columnMetadata.type === Boolean) {\n            return value === true ? 1 : 0\n        } else if (columnMetadata.type === \"date\") {\n            return DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            return DateUtils.mixedDateToTimeString(value)\n        } else if (columnMetadata.type === \"json\") {\n            return JSON.stringify(value)\n        } else if (\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date\n        ) {\n            return DateUtils.mixedDateToDate(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            return DateUtils.simpleJsonToString(value)\n        } else if (\n            columnMetadata.type === \"enum\" ||\n            columnMetadata.type === \"simple-enum\"\n        ) {\n            return \"\" + value\n        } else if (columnMetadata.type === \"set\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        if (\n            columnMetadata.type === Boolean ||\n            columnMetadata.type === \"bool\" ||\n            columnMetadata.type === \"boolean\"\n        ) {\n            value = value ? true : false\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date\n        ) {\n            value = DateUtils.normalizeHydratedDate(value)\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"json\") {\n            value = typeof value === \"string\" ? JSON.parse(value) : value\n        } else if (columnMetadata.type === \"time\") {\n            value = DateUtils.mixedTimeToString(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            value = DateUtils.stringToSimpleJson(value)\n        } else if (\n            (columnMetadata.type === \"enum\" ||\n                columnMetadata.type === \"simple-enum\") &&\n            columnMetadata.enum &&\n            !isNaN(value) &&\n            columnMetadata.enum.indexOf(parseInt(value)) >= 0\n        ) {\n            // convert to number if that exists in possible enum options\n            value = parseInt(value)\n        } else if (columnMetadata.type === \"set\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return value\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if (column.type === Number || column.type === \"integer\") {\n            return \"int\"\n        } else if (column.type === String) {\n            return \"varchar\"\n        } else if (column.type === Date) {\n            return \"datetime\"\n        } else if ((column.type as any) === Buffer) {\n            return \"blob\"\n        } else if (column.type === Boolean) {\n            return \"tinyint\"\n        } else if (column.type === \"uuid\" && !this.uuidColumnTypeSuported) {\n            return \"varchar\"\n        } else if (\n            column.type === \"json\" &&\n            this.options.type === \"mariadb\" &&\n            !VersionUtils.isGreaterOrEqual(this.version, \"10.4.3\")\n        ) {\n            /*\n             * MariaDB implements this as a LONGTEXT rather, as the JSON data type contradicts the SQL standard,\n             * and MariaDB's benchmarks indicate that performance is at least equivalent.\n             *\n             * @see https://mariadb.com/kb/en/json-data-type/\n             * if Version is 10.4.3 or greater, JSON is an alias for longtext and an automatic check_json(column) constraint is added\n             */\n            return \"longtext\"\n        } else if (\n            column.type === \"simple-array\" ||\n            column.type === \"simple-json\"\n        ) {\n            return \"text\"\n        } else if (column.type === \"simple-enum\") {\n            return \"enum\"\n        } else if (\n            column.type === \"double precision\" ||\n            column.type === \"real\"\n        ) {\n            return \"double\"\n        } else if (\n            column.type === \"dec\" ||\n            column.type === \"numeric\" ||\n            column.type === \"fixed\"\n        ) {\n            return \"decimal\"\n        } else if (column.type === \"bool\" || column.type === \"boolean\") {\n            return \"tinyint\"\n        } else if (\n            column.type === \"nvarchar\" ||\n            column.type === \"national varchar\"\n        ) {\n            return \"varchar\"\n        } else if (column.type === \"nchar\" || column.type === \"national char\") {\n            return \"char\"\n        } else {\n            return (column.type as string) || \"\"\n        }\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        const defaultValue = columnMetadata.default\n\n        if (defaultValue === null) {\n            return undefined\n        }\n\n        if (\n            (columnMetadata.type === \"enum\" ||\n                columnMetadata.type === \"simple-enum\" ||\n                typeof defaultValue === \"string\") &&\n            defaultValue !== undefined\n        ) {\n            return `'${defaultValue}'`\n        }\n\n        if (columnMetadata.type === \"set\" && defaultValue !== undefined) {\n            return `'${DateUtils.simpleArrayToString(defaultValue)}'`\n        }\n\n        if (typeof defaultValue === \"number\") {\n            return `'${defaultValue.toFixed(columnMetadata.scale)}'`\n        }\n\n        if (typeof defaultValue === \"boolean\") {\n            return defaultValue ? \"1\" : \"0\"\n        }\n\n        if (typeof defaultValue === \"function\") {\n            const value = defaultValue()\n            return this.normalizeDatetimeFunction(value)\n        }\n\n        if (defaultValue === undefined) {\n            return undefined\n        }\n\n        return `${defaultValue}`\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.indices.some(\n            (idx) =>\n                idx.isUnique &&\n                idx.columns.length === 1 &&\n                idx.columns[0] === column,\n        )\n    }\n\n    /**\n     * Returns default column lengths, which is required on column creation.\n     */\n    getColumnLength(column: ColumnMetadata | TableColumn): string {\n        if (column.length) return column.length.toString()\n\n        /**\n         * fix https://github.com/typeorm/typeorm/issues/1139\n         * note that if the db did support uuid column type it wouldn't have been defaulted to varchar\n         */\n        if (\n            column.generationStrategy === \"uuid\" &&\n            !this.uuidColumnTypeSuported\n        )\n            return \"36\"\n\n        switch (column.type) {\n            case String:\n            case \"varchar\":\n            case \"nvarchar\":\n            case \"national varchar\":\n                return \"255\"\n            case \"varbinary\":\n                return \"255\"\n            default:\n                return \"\"\n        }\n    }\n\n    /**\n     * Creates column type definition including length, precision and scale\n     */\n    createFullType(column: TableColumn): string {\n        let type = column.type\n\n        // used 'getColumnLength()' method, because MySQL requires column length for `varchar`, `nvarchar` and `varbinary` data types\n        if (this.getColumnLength(column)) {\n            type += `(${this.getColumnLength(column)})`\n        } else if (column.width) {\n            type += `(${column.width})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += `(${column.precision},${column.scale})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += `(${column.precision})`\n        }\n\n        if (column.isArray) type += \" array\"\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any> {\n        return new Promise<any>((ok, fail) => {\n            if (this.poolCluster) {\n                this.poolCluster.getConnection(\n                    \"MASTER\",\n                    (err: any, dbConnection: any) => {\n                        err\n                            ? fail(err)\n                            : ok(this.prepareDbConnection(dbConnection))\n                    },\n                )\n            } else if (this.pool) {\n                this.pool.getConnection((err: any, dbConnection: any) => {\n                    err ? fail(err) : ok(this.prepareDbConnection(dbConnection))\n                })\n            } else {\n                fail(\n                    new TypeORMError(\n                        `Connection is not established with mysql database`,\n                    ),\n                )\n            }\n        })\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any> {\n        if (!this.poolCluster) return this.obtainMasterConnection()\n\n        return new Promise<any>((ok, fail) => {\n            this.poolCluster.getConnection(\n                \"SLAVE*\",\n                (err: any, dbConnection: any) => {\n                    err ? fail(err) : ok(this.prepareDbConnection(dbConnection))\n                },\n            )\n        })\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(\n        metadata: EntityMetadata,\n        insertResult: any,\n        entityIndex: number,\n    ) {\n        if (!insertResult) {\n            return undefined\n        }\n\n        if (insertResult.insertId === undefined) {\n            return Object.keys(insertResult).reduce((map, key) => {\n                const column = metadata.findColumnWithDatabaseName(key)\n                if (column) {\n                    OrmUtils.mergeDeep(\n                        map,\n                        column.createValueMap(insertResult[key]),\n                    )\n                    // OrmUtils.mergeDeep(map, column.createValueMap(this.prepareHydratedValue(insertResult[key], column))); // TODO: probably should be like there, but fails on enums, fix later\n                }\n                return map\n            }, {} as ObjectLiteral)\n        }\n\n        const generatedMap = metadata.generatedColumns.reduce(\n            (map, generatedColumn) => {\n                let value: any\n                if (\n                    generatedColumn.generationStrategy === \"increment\" &&\n                    insertResult.insertId\n                ) {\n                    // NOTE: When multiple rows is inserted by a single INSERT statement,\n                    // `insertId` is the value generated for the first inserted row only.\n                    value = insertResult.insertId + entityIndex\n                    // } else if (generatedColumn.generationStrategy === \"uuid\") {\n                    //     console.log(\"getting db value:\", generatedColumn.databaseName);\n                    //     value = generatedColumn.getEntityValue(uuidMap);\n                }\n\n                return OrmUtils.mergeDeep(\n                    map,\n                    generatedColumn.createValueMap(value),\n                )\n            },\n            {} as ObjectLiteral,\n        )\n\n        return Object.keys(generatedMap).length > 0 ? generatedMap : undefined\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) return false // we don't need new columns, we only need exist and changed\n\n            const isColumnChanged =\n                tableColumn.name !== columnMetadata.databaseName ||\n                this.isColumnDataTypeChanged(tableColumn, columnMetadata) ||\n                tableColumn.length !== this.getColumnLength(columnMetadata) ||\n                tableColumn.width !== columnMetadata.width ||\n                (columnMetadata.precision !== undefined &&\n                    tableColumn.precision !== columnMetadata.precision) ||\n                (columnMetadata.scale !== undefined &&\n                    tableColumn.scale !== columnMetadata.scale) ||\n                tableColumn.zerofill !== columnMetadata.zerofill ||\n                tableColumn.unsigned !== columnMetadata.unsigned ||\n                tableColumn.asExpression !== columnMetadata.asExpression ||\n                tableColumn.generatedType !== columnMetadata.generatedType ||\n                tableColumn.comment !==\n                    this.escapeComment(columnMetadata.comment) ||\n                !this.compareDefaultValues(\n                    this.normalizeDefault(columnMetadata),\n                    tableColumn.default,\n                ) ||\n                (tableColumn.enum &&\n                    columnMetadata.enum &&\n                    !OrmUtils.isArraysEqual(\n                        tableColumn.enum,\n                        columnMetadata.enum.map((val) => val + \"\"),\n                    )) ||\n                tableColumn.onUpdate !==\n                    this.normalizeDatetimeFunction(columnMetadata.onUpdate) ||\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                !this.compareNullableValues(columnMetadata, tableColumn) ||\n                tableColumn.isUnique !==\n                    this.normalizeIsUnique(columnMetadata) ||\n                (columnMetadata.generationStrategy !== \"uuid\" &&\n                    tableColumn.isGenerated !== columnMetadata.isGenerated)\n\n            // DEBUG SECTION\n            // if (isColumnChanged) {\n            //     console.log(\"table:\", columnMetadata.entityMetadata.tableName)\n            //     console.log(\n            //         \"name:\",\n            //         tableColumn.name,\n            //         columnMetadata.databaseName,\n            //     )\n            //     console.log(\n            //         \"type:\",\n            //         tableColumn.type,\n            //         this.normalizeType(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"length:\",\n            //         tableColumn.length,\n            //         columnMetadata.length,\n            //     )\n            //     console.log(\"width:\", tableColumn.width, columnMetadata.width)\n            //     console.log(\n            //         \"precision:\",\n            //         tableColumn.precision,\n            //         columnMetadata.precision,\n            //     )\n            //     console.log(\"scale:\", tableColumn.scale, columnMetadata.scale)\n            //     console.log(\n            //         \"zerofill:\",\n            //         tableColumn.zerofill,\n            //         columnMetadata.zerofill,\n            //     )\n            //     console.log(\n            //         \"unsigned:\",\n            //         tableColumn.unsigned,\n            //         columnMetadata.unsigned,\n            //     )\n            //     console.log(\n            //         \"asExpression:\",\n            //         tableColumn.asExpression,\n            //         columnMetadata.asExpression,\n            //     )\n            //     console.log(\n            //         \"generatedType:\",\n            //         tableColumn.generatedType,\n            //         columnMetadata.generatedType,\n            //     )\n            //     console.log(\n            //         \"comment:\",\n            //         tableColumn.comment,\n            //         this.escapeComment(columnMetadata.comment),\n            //     )\n            //     console.log(\n            //         \"default:\",\n            //         tableColumn.default,\n            //         this.normalizeDefault(columnMetadata),\n            //     )\n            //     console.log(\"enum:\", tableColumn.enum, columnMetadata.enum)\n            //     console.log(\n            //         \"default changed:\",\n            //         !this.compareDefaultValues(\n            //             this.normalizeDefault(columnMetadata),\n            //             tableColumn.default,\n            //         ),\n            //     )\n            //     console.log(\n            //         \"isPrimary:\",\n            //         tableColumn.isPrimary,\n            //         columnMetadata.isPrimary,\n            //     )\n            //     console.log(\n            //         \"isNullable changed:\",\n            //         !this.compareNullableValues(columnMetadata, tableColumn),\n            //     )\n            //     console.log(\n            //         \"isUnique:\",\n            //         tableColumn.isUnique,\n            //         this.normalizeIsUnique(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"isGenerated:\",\n            //         tableColumn.isGenerated,\n            //         columnMetadata.isGenerated,\n            //     )\n            //     console.log(\n            //         columnMetadata.generationStrategy !== \"uuid\" &&\n            //             tableColumn.isGenerated !== columnMetadata.isGenerated,\n            //     )\n            //     console.log(\"==========================================\")\n            // }\n\n            return isColumnChanged\n        })\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(returningType: ReturningType): boolean {\n        return this._isReturningSqlSupported[returningType]\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return \"?\"\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads all driver dependencies.\n     */\n    protected loadDependencies(): void {\n        const connectorPackage = this.options.connectorPackage ?? \"mysql\"\n        const fallbackConnectorPackage =\n            connectorPackage === \"mysql\"\n                ? (\"mysql2\" as const)\n                : (\"mysql\" as const)\n        try {\n            // try to load first supported package\n            const mysql =\n                this.options.driver || PlatformTools.load(connectorPackage)\n            this.mysql = mysql\n            /*\n             * Some frameworks (such as Jest) may mess up Node's require cache and provide garbage for the 'mysql' module\n             * if it was not installed. We check that the object we got actually contains something otherwise we treat\n             * it as if the `require` call failed.\n             *\n             * @see https://github.com/typeorm/typeorm/issues/1373\n             */\n            if (Object.keys(this.mysql).length === 0) {\n                throw new TypeORMError(\n                    `'${connectorPackage}' was found but it is empty. Falling back to '${fallbackConnectorPackage}'.`,\n                )\n            }\n        } catch (e) {\n            try {\n                this.mysql = PlatformTools.load(fallbackConnectorPackage) // try to load second supported package\n            } catch (e) {\n                throw new DriverPackageNotInstalledError(\n                    \"Mysql\",\n                    connectorPackage,\n                )\n            }\n        }\n    }\n\n    /**\n     * Creates a new connection pool for a given database credentials.\n     */\n    protected createConnectionOptions(\n        options: MysqlConnectionOptions,\n        credentials: MysqlConnectionCredentialsOptions,\n    ): Promise<any> {\n        credentials = Object.assign(\n            {},\n            credentials,\n            DriverUtils.buildDriverOptions(credentials),\n        ) // todo: do it better way\n\n        // build connection options for the driver\n        return Object.assign(\n            {},\n            {\n                charset: options.charset,\n                timezone: options.timezone,\n                connectTimeout: options.connectTimeout,\n                insecureAuth: options.insecureAuth,\n                supportBigNumbers:\n                    options.supportBigNumbers !== undefined\n                        ? options.supportBigNumbers\n                        : true,\n                bigNumberStrings:\n                    options.bigNumberStrings !== undefined\n                        ? options.bigNumberStrings\n                        : true,\n                dateStrings: options.dateStrings,\n                debug: options.debug,\n                trace: options.trace,\n                multipleStatements: options.multipleStatements,\n                flags: options.flags,\n            },\n            {\n                host: credentials.host,\n                user: credentials.username,\n                password: credentials.password,\n                database: credentials.database,\n                port: credentials.port,\n                ssl: options.ssl,\n                socketPath: credentials.socketPath,\n            },\n            options.acquireTimeout === undefined\n                ? {}\n                : { acquireTimeout: options.acquireTimeout },\n            { connectionLimit: options.poolSize },\n            options.extra || {},\n        )\n    }\n\n    /**\n     * Creates a new connection pool for a given database credentials.\n     */\n    protected createPool(connectionOptions: any): Promise<any> {\n        // create a connection pool\n        const pool = this.mysql.createPool(connectionOptions)\n\n        // make sure connection is working fine\n        return new Promise<void>((ok, fail) => {\n            // (issue #610) we make first connection to database to make sure if connection credentials are wrong\n            // we give error before calling any other method that creates actual query runner\n            pool.getConnection((err: any, connection: any) => {\n                if (err) return pool.end(() => fail(err))\n\n                connection.release()\n                ok(pool)\n            })\n        })\n    }\n\n    /**\n     * Attaches all required base handlers to a database connection, such as the unhandled error handler.\n     */\n    private prepareDbConnection(connection: any): any {\n        const { logger } = this.connection\n        /*\n         * Attaching an error handler to connection errors is essential, as, otherwise, errors raised will go unhandled and\n         * cause the hosting app to crash.\n         */\n        if (connection.listeners(\"error\").length === 0) {\n            connection.on(\"error\", (error: any) =>\n                logger.log(\n                    \"warn\",\n                    `MySQL connection raised an error. ${error}`,\n                ),\n            )\n        }\n        return connection\n    }\n\n    /**\n     * Checks if \"DEFAULT\" values in the column metadata and in the database are equal.\n     */\n    protected compareDefaultValues(\n        columnMetadataValue: string | undefined,\n        databaseValue: string | undefined,\n    ): boolean {\n        if (\n            typeof columnMetadataValue === \"string\" &&\n            typeof databaseValue === \"string\"\n        ) {\n            // we need to cut out \"'\" because in mysql we can understand returned value is a string or a function\n            // as result compare cannot understand if default is really changed or not\n            columnMetadataValue = columnMetadataValue.replace(/^'+|'+$/g, \"\")\n            databaseValue = databaseValue.replace(/^'+|'+$/g, \"\")\n        }\n\n        return columnMetadataValue === databaseValue\n    }\n\n    compareNullableValues(\n        columnMetadata: ColumnMetadata,\n        tableColumn: TableColumn,\n    ): boolean {\n        // MariaDB does not support NULL/NOT NULL expressions for generated columns\n        const isMariaDb = this.options.type === \"mariadb\"\n        if (isMariaDb && columnMetadata.generatedType) {\n            return true\n        }\n\n        return columnMetadata.isNullable === tableColumn.isNullable\n    }\n\n    /**\n     * If parameter is a datetime function, e.g. \"CURRENT_TIMESTAMP\", normalizes it.\n     * Otherwise returns original input.\n     */\n    protected normalizeDatetimeFunction(value?: string) {\n        if (!value) return value\n\n        // check if input is datetime function\n        const isDatetimeFunction =\n            value.toUpperCase().indexOf(\"CURRENT_TIMESTAMP\") !== -1 ||\n            value.toUpperCase().indexOf(\"NOW\") !== -1\n\n        if (isDatetimeFunction) {\n            // extract precision, e.g. \"(3)\"\n            const precision = value.match(/\\(\\d+\\)/)\n            if (this.options.type === \"mariadb\") {\n                return precision\n                    ? `CURRENT_TIMESTAMP${precision[0]}`\n                    : \"CURRENT_TIMESTAMP()\"\n            } else {\n                return precision\n                    ? `CURRENT_TIMESTAMP${precision[0]}`\n                    : \"CURRENT_TIMESTAMP\"\n            }\n        } else {\n            return value\n        }\n    }\n\n    /**\n     * Escapes a given comment.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment) return comment\n\n        comment = comment.replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return comment\n    }\n\n    /**\n     * A helper to check if column data types have changed\n     * This can be used to manage checking any types the\n     * database may alias\n     */\n    private isColumnDataTypeChanged(\n        tableColumn: TableColumn,\n        columnMetadata: ColumnMetadata,\n    ) {\n        // this is an exception for mariadb versions where json is an alias for longtext\n        if (\n            this.normalizeType(columnMetadata) === \"json\" &&\n            tableColumn.type.toLowerCase() === \"longtext\"\n        )\n            return false\n        return tableColumn.type !== this.normalizeType(columnMetadata)\n    }\n}\n"], "sourceRoot": "../.."}