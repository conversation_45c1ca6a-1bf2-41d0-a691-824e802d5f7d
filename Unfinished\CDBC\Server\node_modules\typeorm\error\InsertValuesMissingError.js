"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsertValuesMissingError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when user tries to insert using QueryBuilder but do not specify what to insert.
 */
class InsertValuesMissingError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`Cannot perform insert query because values are not defined. ` +
            `Call "qb.values(...)" method to specify inserted values.`);
    }
}
exports.InsertValuesMissingError = InsertValuesMissingError;

//# sourceMappingURL=InsertValuesMissingError.js.map
