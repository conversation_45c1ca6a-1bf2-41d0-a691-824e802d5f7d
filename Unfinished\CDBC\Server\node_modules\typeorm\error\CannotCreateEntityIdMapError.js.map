{"version": 3, "sources": ["../../src/error/CannotCreateEntityIdMapError.ts"], "names": [], "mappings": ";;;AAEA,iDAA6C;AAE7C;;;GAGG;AACH,MAAa,4BAA6B,SAAQ,2BAAY;IAC1D,YAAY,QAAwB,EAAE,EAAO;QACzC,KAAK,EAAE,CAAA;QAEP,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAChD,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE;YACtB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAA;YACxC,OAAO,MAAM,CAAA;QACjB,CAAC,EACD,EAAmB,CACtB,CAAA;QACD,IAAI,CAAC,OAAO,GAAG,+BAA+B,EAAE,cAC5C,QAAQ,CAAC,UACb,mFAAmF,IAAI,CAAC,SAAS,CAC7F,aAAa,CAChB,YAAY,CAAA;IACjB,CAAC;CACJ;AAjBD,oEAiBC", "file": "CannotCreateEntityIdMapError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to create entity id map from the mixed id value,\n * but id value is a single value when entity requires multiple values.\n */\nexport class CannotCreateEntityIdMapError extends TypeORMError {\n    constructor(metadata: EntityMetadata, id: any) {\n        super()\n\n        const objectExample = metadata.primaryColumns.reduce(\n            (object, column, index) => {\n                column.setEntityValue(object, index + 1)\n                return object\n            },\n            {} as ObjectLiteral,\n        )\n        this.message = `Cannot use given entity id \"${id}\" because \"${\n            metadata.targetName\n        }\" contains multiple primary columns, you must provide object in following form: ${JSON.stringify(\n            objectExample,\n        )} as an id.`\n    }\n}\n"], "sourceRoot": ".."}