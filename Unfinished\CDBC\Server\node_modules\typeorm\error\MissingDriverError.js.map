{"version": 3, "sources": ["../../src/error/MissingDriverError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,kBAAmB,SAAQ,2BAAY;IAChD,YAAY,UAAkB,EAAE,mBAA6B,EAAE;QAC3D,KAAK,CACD,kBAAkB,UAAU,kCAAkC;YAC1D,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAC7D,CAAA;IACL,CAAC;CACJ;AAPD,gDAOC", "file": "MissingDriverError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer specifies driver type that does not exist or supported.\n */\nexport class MissingDriverError extends TypeORMError {\n    constructor(driverType: string, availableDrivers: string[] = []) {\n        super(\n            `Wrong driver: \"${driverType}\" given. Supported drivers are: ` +\n                `${availableDrivers.map((d) => `\"${d}\"`).join(\", \")}.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}