{"version": 3, "sources": ["../../src/driver/mysql/MysqlQueryRunner.ts"], "names": [], "mappings": ";;;AACA,uCAA0C;AAC1C,mEAA+D;AAC/D,iGAA6F;AAC7F,uFAAmF;AAEnF,wEAAoE;AACpE,gEAA4D;AAG5D,4DAAwD;AAExD,wEAAoE;AAEpE,gFAA4E;AAC5E,sEAAkE;AAClE,wEAAoE;AACpE,yDAAqD;AACrD,8DAA0D;AAC1D,0EAAsE;AACtE,gEAA4D;AAC5D,kDAA8C;AAC9C,0DAAsD;AACtD,oCAAgC;AAGhC,kEAA8D;AAI9D;;GAEG;AACH,MAAa,gBAAiB,SAAQ,iCAAe;IAmBjD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAmB,EAAE,IAAqB;QAClD,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,OAAO;QACH,IAAI,IAAI,CAAC,kBAAkB;YACvB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QAEnD,IAAI,IAAI,CAAC,yBAAyB;YAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAA;QAEzC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACpD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,qBAAqB,EAAE;iBACvB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjB,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBACpC,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;aAAM,CAAC;YACJ,SAAS;YACT,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,sBAAsB,EAAE;iBACxB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjB,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBACpC,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAA;IACzC,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,IAAI,CAAC,kBAAkB;YAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAA;QAC9D,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,KAAK,CACZ,kCAAkC,GAAG,cAAc,CACtD,CAAA;YACL,CAAC;YACD,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACzC,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAClE,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,6BAA6B,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC3D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,iCAAiC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YAC5B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC;gBACD,MAAM,kBAAkB,GACpB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAA;gBAC1C,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;gBAC7C,MAAM,YAAY,GACd,kBAAkB,IAAI,qBAAqB;oBACvC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,qBAAqB,EAAE;oBAChD,CAAC,CAAC,KAAK,CAAA;gBACf,kBAAkB,CAAC,KAAK,CACpB,YAAY,EACZ,UAAU,EACV,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;oBACzB,oDAAoD;oBACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;oBAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;oBAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;oBAExD,IACI,qBAAqB;wBACrB,kBAAkB,GAAG,qBAAqB;wBAE1C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBAEL,IAAI,GAAG,EAAE,CAAC;wBACN,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;wBACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;wBAED,OAAO,IAAI,CACP,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAC/C,CAAA;oBACL,CAAC;oBAED,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;oBAED,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;oBAEhC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAA;oBAEhB,IAAI,CAAC;wBACD,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;oBACpC,CAAC;oBAAC,MAAM,CAAC;wBACL,cAAc;oBAClB,CAAC;oBAED,IAAI,GAAG,EAAE,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;wBACtC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAA;oBACtC,CAAC;oBAED,IAAI,mBAAmB,EAAE,CAAC;wBACtB,EAAE,CAAC,MAAM,CAAC,CAAA;oBACd,CAAC;yBAAM,CAAC;wBACJ,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;oBAClB,CAAC;gBACL,CAAC,CACJ,CAAA;YACL,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,CAAC;oBAAS,CAAC;gBACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAClC,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CACF,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC;gBACD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;gBAC/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;gBAC/D,MAAM,aAAa,GAAG,kBAAkB,CAAC,KAAK,CAC1C,KAAK,EACL,UAAU,CACb,CAAA;gBACD,IAAI,KAAK;oBAAE,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;gBACzC,IAAI,OAAO;oBAAE,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;gBAC/C,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAA;YAC9B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,MAAM,IAAI,oBAAY,CAAC,6CAA6C,CAAC,CAAA;IACzE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,8EAA8E,QAAQ,GAAG,CAC5F,CAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAA;QAClE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,IAAI,oBAAY,CAAC,6CAA6C,CAAC,CAAA;IACzE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAA;QACpE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC/D,MAAM,GAAG,GAAG,8EAA8E,eAAe,CAAC,QAAQ,2BAA2B,eAAe,CAAC,SAAS,GAAG,CAAA;QACzK,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAA4B;QAE5B,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC/D,MAAM,UAAU,GAAG,iCAAe,CAAC,aAAa,CAAC,MAAM,CAAC;YACpD,CAAC,CAAC,MAAM,CAAC,IAAI;YACb,CAAC,CAAC,MAAM,CAAA;QACZ,MAAM,GAAG,GAAG,8EAA8E,eAAe,CAAC,QAAQ,2BAA2B,eAAe,CAAC,SAAS,4BAA4B,UAAU,GAAG,CAAA;QAC/M,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,MAAM,EAAE,GAAG,UAAU;YACjB,CAAC,CAAC,mCAAmC,QAAQ,IAAI;YACjD,CAAC,CAAC,qBAAqB,QAAQ,IAAI,CAAA;QACvC,MAAM,IAAI,GAAG,mBAAmB,QAAQ,IAAI,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,MAAM,EAAE,GAAG,OAAO;YACd,CAAC,CAAC,6BAA6B,QAAQ,IAAI;YAC3C,CAAC,CAAC,mBAAmB,QAAQ,IAAI,CAAA;QACrC,MAAM,IAAI,GAAG,qBAAqB,QAAQ,IAAI,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,MAAM,IAAI,oBAAY,CAClB,0DAA0D,CAC7D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAiB;QAClD,MAAM,IAAI,oBAAY,CAClB,wDAAwD,CAC3D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI;QAEjC,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QACD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,kGAAkG;QAClG,+GAA+G;QAC/G,oCAAoC;QAEpC,gIAAgI;QAChI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAC5B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CACpD,CAAA;QAED,iFAAiF;QACjF,kIAAkI;QAClI,IAAI,iBAAiB;YACjB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC9D,CAAA;QAEL,4FAA4F;QAC5F,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAEvD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACtD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,MAAsB,EACtB,OAAiB,EACjB,kBAA2B,IAAI;QAE/B,qGAAqG;QACrG,wDAAwD;QACxD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAC3C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAClD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,eAAe;YACf,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC5D,CAAA;QAEL,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAClD,CAAA;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAEvD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,IAAU,EACV,mBAA4B,KAAK;QAEjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,iCAAe,CAAC,OAAO,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAEzD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC,YAAY,CAAA;QAEvE,eAAe;QACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,gBAAgB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,UAAU,CAC3D,QAAQ,CACX,EAAE,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,gBAAgB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,UAAU,CAC3D,QAAQ,CACX,EAAE,CACN,CACJ,CAAA;QAED,2BAA2B;QAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,CACpB,CAAA;YAED,0DAA0D;YAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAM;YAEvC,4BAA4B;YAC5B,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW;iBAChC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;iBAChC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,gBAAgB;YAChB,IAAI,SAAS,GAAG,EAAE,CAAA;YAClB,IAAI,KAAK,CAAC,QAAQ;gBAAE,SAAS,IAAI,SAAS,CAAA;YAC1C,IAAI,KAAK,CAAC,SAAS;gBAAE,SAAS,IAAI,UAAU,CAAA;YAC5C,IAAI,KAAK,CAAC,UAAU;gBAAE,SAAS,IAAI,WAAW,CAAA;YAC9C,MAAM,WAAW,GACb,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM;gBAC5B,CAAC,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE;gBAChC,CAAC,CAAC,EAAE,CAAA;YAEZ,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,iBACpC,KAAK,CAAC,IACV,WAAW,SAAS,WAAW,YAAY,OAAO,WAAW,IAAI,WAAW,EAAE,CACjF,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,iBAAiB,YAAY,WAAW,SAAS,WAC9C,KAAK,CAAC,IACV,OAAO,WAAW,IAAI,WAAW,EAAE,CACtC,CACJ,CAAA;YAED,0BAA0B;YAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,gCAAgC;QAChC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gEAAgE;YAChE,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB;gBAAE,OAAM;YAEjD,4BAA4B;YAC5B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;iBACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;iBAChC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;iBACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;iBAChC,IAAI,CAAC,GAAG,CAAC,CAAA;YACd,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gBAAgB;YAChB,IAAI,EAAE,GACF,eAAe,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,uBACpC,UAAU,CAAC,IACf,wBAAwB,iBAAiB,mBAAmB,WAAW,IAAI;gBAC3E,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;YACjC,IAAI,UAAU,CAAC,QAAQ;gBAAE,EAAE,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;YAClE,IAAI,UAAU,CAAC,QAAQ;gBAAE,EAAE,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;YAElE,IAAI,IAAI,GACJ,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,iBAAiB,wBACrC,UAAU,CAAC,IACf,mBAAmB,WAAW,IAAI;gBAClC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;YACjC,IAAI,UAAU,CAAC,QAAQ;gBAAE,IAAI,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;YACpE,IAAI,UAAU,CAAC,QAAQ;gBAAE,IAAI,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;YAEpE,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,CAAC,CAAA;YAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAEjC,0BAA0B;YAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,oDAAoD;QACpD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CACpB,WAA2B,EAC3B,UAAmB;QAEnB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAEjD,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;YACzB,OAAM;QACV,CAAC;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAE9B,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,YAAY,UAAU,EAAE,CAC5B,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,OAAO,EAAE,CAC7D,CACJ,CAAA;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,wDAAwD;QACxD,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,MAAM,sBAAsB,GAAG,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAA;QAEpE,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAC9B,MAAM,EACN,sBAAsB,EACtB,KAAK,CACR,EAAE,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,MAAM,CAAC,IACX,IAAI,CACP,CACJ,CAAA;QAED,0CAA0C;QAC1C,IAAI,MAAM,CAAC,SAAS,IAAI,sBAAsB,EAAE,CAAC;YAC7C,uFAAuF;YACvF,MAAM,eAAe,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC5C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW;gBAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW,CAChD,CAAA;YACD,IAAI,eAAe,EAAE,CAAC;gBAClB,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;gBAClD,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;gBACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;gBACjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,MAAM,CAAC,IACX,MAAM,IAAI,CAAC,oBAAoB,CAC3B,kBAAkB,EAClB,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAClD,CACJ,CAAA;YACL,CAAC;YAED,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;YACjD,IAAI,WAAW,GAAG,cAAc;iBAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;iBACrC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAC3D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;YAED,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3B,WAAW,GAAG,cAAc;iBACvB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;iBACrC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAC3D,CACJ,CAAA;YAED,0EAA0E;YAC1E,IAAI,eAAe,EAAE,CAAC;gBAClB,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;gBAClD,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;gBACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;gBACjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAClD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,MAAM,CAAC,IACX,MAAM,IAAI,CAAC,oBAAoB,CAC3B,kBAAkB,EAClB,IAAI,CACP,EAAE,CACN,CACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;YACvD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,IAAI,uBAAU,CAAC;gBAC/B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;oBAClD,MAAM,CAAC,IAAI;iBACd,CAAC;gBACF,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC1B,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAA;YACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,WAAW,CAAC,OAAO,CAAC,IAAI,CACpB,IAAI,yBAAW,CAAC;gBACZ,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;aACvC,CAAC,CACL,CAAA;YACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,uBACjC,WAAW,CAAC,IAChB,SAAS,MAAM,CAAC,IAAI,KAAK,CAC5B,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,WAAW,CAAC,IAChB,IAAI,CACP,CACJ,CAAA;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IAAI,SAAS,GAA4B,SAAS,CAAA;QAClD,IAAI,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,eAAqC,EACrC,SAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,eAAe,CAAC;YAC5D,CAAC,CAAC,eAAe;YACjB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,eAAe,CAAC,CAAA;QACrE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,eAAe,2BAA2B,KAAK,CAAC,IAAI,UAAU,CAC5E,CAAA;QAEL,IACI,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW;YAC5C,SAAS,CAAC,kBAAkB,KAAK,MAAM,CAAC;YAC5C,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YACrC,CAAC,SAAS,CAAC,aAAa;gBACpB,SAAS,CAAC,aAAa;gBACvB,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa,CAAC;YACxD,CAAC,CAAC,SAAS,CAAC,aAAa;gBACrB,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC;YAC1C,CAAC,SAAS,CAAC,aAAa,KAAK,SAAS,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EACrE,CAAC;YACC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAEtC,sBAAsB;YACtB,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACpC,yDAAyD;gBACzD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,SAAS,CAAC,IACd,QAAQ,SAAS,CAAC,IAAI,MAAM,IAAI,CAAC,oBAAoB,CACjD,SAAS,EACT,IAAI,EACJ,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,SAAS,CAAC,IACd,QAAQ,SAAS,CAAC,IAAI,MAAM,IAAI,CAAC,oBAAoB,CACjD,SAAS,EACT,IAAI,EACJ,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBAED,2BAA2B;gBAC3B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvD,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,CACpB,CAAA;oBAEL,0DAA0D;oBAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa;wBAAE,OAAM;oBAExC,4BAA4B;oBAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CACpB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EACzC,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACtC,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW;yBAChC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;yBAChC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,gBAAgB;oBAChB,IAAI,SAAS,GAAG,EAAE,CAAA;oBAClB,IAAI,KAAK,CAAC,QAAQ;wBAAE,SAAS,IAAI,SAAS,CAAA;oBAC1C,IAAI,KAAK,CAAC,SAAS;wBAAE,SAAS,IAAI,UAAU,CAAA;oBAC5C,IAAI,KAAK,CAAC,UAAU;wBAAE,SAAS,IAAI,WAAW,CAAA;oBAC9C,MAAM,WAAW,GACb,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM;wBAC5B,CAAC,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE;wBAChC,CAAC,CAAC,EAAE,CAAA;oBAEZ,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBACG,KAAK,CAAC,IACV,WAAW,SAAS,WAAW,YAAY,OAAO,WAAW,IAAI,WAAW,EAAE,CACjF,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBAAiB,YAAY,WAAW,SAAS,WAC9C,KAAK,CAAC,IACV,OAAO,WAAW,IAAI,WAAW,EAAE,CACtC,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;gBAC7B,CAAC,CAAC,CAAA;gBAEF,iCAAiC;gBACjC,WAAW;qBACN,qBAAqB,CAAC,SAAS,CAAC;qBAChC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACpB,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gEAAgE;oBAChE,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc;wBAAE,OAAM;oBAE9C,4BAA4B;oBAC5B,UAAU,CAAC,WAAW,CAAC,MAAM,CACzB,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;yBACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;yBAChC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,MAAM,qBAAqB,GACvB,UAAU,CAAC,qBAAqB;yBAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;yBAChC,IAAI,CAAC,GAAG,CAAC,CAAA;oBAClB,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gBAAgB;oBAChB,IAAI,EAAE,GACF,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBACG,UAAU,CAAC,IACf,wBAAwB,iBAAiB,mBAAmB,WAAW,IAAI;wBAC3E,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;oBACjC,IAAI,UAAU,CAAC,QAAQ;wBACnB,EAAE,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;oBAC7C,IAAI,UAAU,CAAC,QAAQ;wBACnB,EAAE,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;oBAE7C,IAAI,IAAI,GACJ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,iBAAiB,wBACrC,UAAU,CAAC,IACf,mBAAmB,WAAW,IAAI;wBAClC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;oBACjC,IAAI,UAAU,CAAC,QAAQ;wBACnB,IAAI,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;oBAC/C,IAAI,UAAU,CAAC,QAAQ;wBACnB,IAAI,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;oBAE/C,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;oBAEjC,0BAA0B;oBAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;gBACvC,CAAC,CAAC,CAAA;gBAEN,wCAAwC;gBACxC,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;gBACD,WAAW,CAAC,OAAO,CACf,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAe,CAAC,CAC/C,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;gBACvB,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;YACnC,CAAC;YAED,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;gBACzD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,SAAS,CAAC,IACd,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CACrD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,SAAS,CAAC,IACd,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CACrD,CACJ,CAAA;gBAED,IAAI,SAAS,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;oBACtD,yFAAyF;oBAEzF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;oBACvD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;wBAC9C,MAAM,EAAE,eAAe;wBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;wBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;qBACvB,CAAC,CAAA;oBACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;wBAC9C,MAAM,EAAE,eAAe;wBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;wBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,KAAK,EAAE,SAAS,CAAC,YAAY;qBAChC,CAAC,CAAA;oBAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;oBAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBACjC,CAAC;qBAAM,IACH,CAAC,SAAS,CAAC,aAAa;oBACxB,SAAS,CAAC,aAAa,EACzB,CAAC;oBACC,yFAAyF;oBAEzF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;oBACvD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;wBAC9C,MAAM,EAAE,eAAe;wBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;wBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,KAAK,EAAE,SAAS,CAAC,YAAY;qBAChC,CAAC,CAAA;oBACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;wBAC9C,MAAM,EAAE,eAAe;wBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;wBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;qBACvB,CAAC,CAAA;oBAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;oBAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBACjC,CAAC;qBAAM,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY,EAAE,CAAC;oBAC3D,uEAAuE;oBACvE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;oBACvD,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU;yBAC9B,kBAAkB,EAAE;yBACpB,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;yBAC1C,GAAG,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,YAAY,EAAE,CAAC;yBACtC,KAAK,CAAC,gBAAgB,EAAE;wBACrB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;qBAC3C,CAAC;yBACD,QAAQ,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;yBACpD,QAAQ,CAAC,oBAAoB,EAAE;wBAC5B,MAAM,EAAE,eAAe;qBAC1B,CAAC;yBACD,QAAQ,CAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;yBACnD,qBAAqB,EAAE,CAAA;oBAE5B,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU;yBACpC,kBAAkB,EAAE;yBACpB,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;yBAC1C,GAAG,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,YAAY,EAAE,CAAC;yBACtC,KAAK,CAAC,gBAAgB,EAAE;wBACrB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;qBAC3C,CAAC;yBACD,QAAQ,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;yBACpD,QAAQ,CAAC,oBAAoB,EAAE;wBAC5B,MAAM,EAAE,eAAe;qBAC1B,CAAC;yBACD,QAAQ,CAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;yBACnD,qBAAqB,EAAE,CAAA;oBAE5B,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBACzD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CACxD,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC9C,mGAAmG;gBACnG,MAAM,eAAe,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC5C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW;oBAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW,CAChD,CAAA;gBACD,IAAI,eAAe,EAAE,CAAC;oBAClB,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;oBAClD,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;oBACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;oBAEjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,eAAe,CAAC,IACpB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,kBAAkB,EAClB,IAAI,CACP,EAAE,CACN,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,eAAe,EACf,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBACL,CAAC;gBAED,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;gBAEjD,2EAA2E;gBAC3E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;yBACrC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,mBAAmB,CACvB,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC/B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAC9B,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,IAAI,CAAA;oBACxB,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;yBACrC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,mBAAmB,CACvB,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CACnC,CAAA;oBACD,cAAc,CAAC,MAAM,CACjB,cAAc,CAAC,OAAO,CAAC,aAAc,CAAC,EACtC,CAAC,CACJ,CAAA;oBACD,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,KAAK,CAAA;oBAEzB,gEAAgE;oBAChE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,MAAM,WAAW,GAAG,cAAc;6BAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;6BACrC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,mBAAmB,CACvB,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;gBAED,oGAAoG;gBACpG,IAAI,eAAe,EAAE,CAAC;oBAClB,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;oBAClD,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;oBACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;oBAEjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,eAAe,EACf,IAAI,CACP,EAAE,CACN,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,eAAe,CAAC,IACpB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,kBAAkB,EAClB,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBAC9B,MAAM,WAAW,GAAG,IAAI,uBAAU,CAAC;wBAC/B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;4BAClD,SAAS,CAAC,IAAI;yBACjB,CAAC;wBACF,WAAW,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;wBAC7B,QAAQ,EAAE,IAAI;qBACjB,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;oBACrC,WAAW,CAAC,OAAO,CAAC,IAAI,CACpB,IAAI,yBAAW,CAAC;wBACZ,IAAI,EAAE,WAAW,CAAC,IAAI;wBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;qBACvC,CAAC,CACL,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,WAAW,CAAC,IAAI,SACpC,SAAS,CAAC,IACd,KAAK,CACR,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBAAiB,WAAW,CAAC,IAAI,IAAI,CACzC,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACnD,OAAO,CACH,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;4BAC9B,KAAK,CAAC,QAAQ,KAAK,IAAI;4BACvB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CACpB,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,CAChD,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAY,CAAC,EACzC,CAAC,CACJ,CAAA;oBAED,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,WAAY,CAAC,IAAI,CAChD,CAAA;oBACD,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAY,CAAC,EACzC,CAAC,CACJ,CAAA;oBAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBAAiB,WAAY,CAAC,IAAI,IAAI,CAC1C,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,WAAY,CAAC,IAAI,SACrC,SAAS,CAAC,IACd,KAAK,CACR,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,KAAK,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,oBAAY,CAClB,WAAW,YAAY,6BAA6B,KAAK,CAAC,IAAI,GAAG,CACpE,CAAA;QAEL,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,8BAA8B;QAC9B,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,mGAAmG;YACnG,MAAM,eAAe,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC5C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW;gBAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW,CAChD,CAAA;YACD,IAAI,eAAe,EAAE,CAAC;gBAClB,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;gBAClD,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;gBACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;gBAEjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,eAAe,CAAC,IACpB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,kBAAkB,EAClB,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,eAAe,EACf,IAAI,CACP,EAAE,CACN,CACJ,CAAA;YACL,CAAC;YAED,kCAAkC;YAClC,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;iBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,KAAK,aAAa,CAAC,IAAI,IAAI,CAAC;iBACnD,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,mBAAmB,CACvB,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;YAED,yBAAyB;YACzB,MAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC7D,WAAY,CAAC,SAAS,GAAG,KAAK,CAAA;YAE9B,mFAAmF;YACnF,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;qBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,KAAK,aAAa,CAAC,IAAI,IAAI,CAAC;qBACnD,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,mBAAmB,CACvB,CACJ,CAAA;YACL,CAAC;YAED,oJAAoJ;YACpJ,IAAI,eAAe,IAAI,eAAe,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC1D,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;gBAClD,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;gBACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;gBAEjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,eAAe,EACf,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,eAAe,CAAC,IACpB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,kBAAkB,EAClB,IAAI,CACP,EAAE,CACN,CACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC7D,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACzB,6DAA6D;YAC7D,MAAM,UAAU,GACZ,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE;gBACvD,MAAM,CAAC,IAAI;aACd,CAAC,CAAA;YACN,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,UAAU,CACzC,CAAA;YACD,IAAI,WAAW;gBACX,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YAEL,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;gBAC9D,MAAM,CAAC,IAAI;aACd,CAAC,CAAA;YACF,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CACtC,CAAA;YACD,IAAI,UAAU;gBACV,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EACvC,CAAC,CACJ,CAAA;YAEL,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBAAiB,SAAS,IAAI,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,SAAS,SAAS,MAAM,CAAC,IAAI,KAAK,CAC7D,CACJ,CAAA;QACL,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,MAAM,CAAC,IACX,IAAI,CACP,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CACrD,CACJ,CAAA;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;YACvD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB;QAErB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QACvD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAE1C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACxD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,mGAAmG;QACnG,MAAM,eAAe,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC5C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,kBAAkB,KAAK,WAAW,CACtE,CAAA;QACD,IAAI,eAAe,EAAE,CAAC;YAClB,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;YAClD,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;YACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;YAEjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,eAAe,CAAC,IACpB,MAAM,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAC9D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAC3D,CACJ,CAAA;QACL,CAAC;QAED,4DAA4D;QAC5D,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;QACjD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;iBACrC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAC3D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;QACL,CAAC;QAED,2BAA2B;QAC3B,WAAW,CAAC,OAAO;aACd,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAEnD,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;aACxC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,iBAAiB,GAAG,CAC7C,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,CACtE,CAAA;QAED,8IAA8I;QAC9I,MAAM,yBAAyB,GAAG,eAAe;YAC7C,CAAC,CAAC,eAAe;YACjB,CAAC,CAAC,OAAO,CAAC,IAAI,CACR,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW;gBAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW,CAChD,CAAA;QACP,IAAI,yBAAyB,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,yBAAyB,CAAC,KAAK,EAAE,CAAA;YAC5D,kBAAkB,CAAC,WAAW,GAAG,KAAK,CAAA;YACtC,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAA;YAEjD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,kBAAkB,CAAC,IACvB,MAAM,IAAI,CAAC,oBAAoB,CAC3B,yBAAyB,EACzB,IAAI,CACP,EAAE,CACN,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aACjC,yBAAyB,CAAC,IAC9B,MAAM,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAC9D,CACJ,CAAA;YAED,6DAA6D;YAC7D,MAAM,sBAAsB,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,yBAAyB,CAAC,IAAI,CAC7D,CAAA;YACD,sBAAuB,CAAC,WAAW,GAAG,IAAI,CAAA;YAC1C,sBAAuB,CAAC,kBAAkB,GAAG,WAAW,CAAA;QAC5D,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,WAA2B;QAC5C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CACjC,KAAK,EACL,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CACpD,CAAA;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,IAAI,oBAAY,CAClB,sEAAsE,CACzE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,oBAAY,CAClB,sEAAsE,CACzE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,IAAI,oBAAY,CAClB,sEAAsE,CACzE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,oBAAY,CAClB,sEAAsE,CACzE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,IAAI,oBAAY,CAAC,2CAA2C,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,IAAI,oBAAY,CAAC,2CAA2C,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,IAAI,oBAAY,CAAC,2CAA2C,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,IAAI,oBAAY,CAAC,2CAA2C,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,oBAAY,CAAC,+CAA+C,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAAC,+CAA+C,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,oBAAY,CAAC,+CAA+C,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAAC,+CAA+C,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,gFAAgF;QAChF,IAAI,CAAC,UAAU,CAAC,IAAI;YAChB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CACjD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,UAAU,GAAG,iCAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAClE,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,oBAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,CAC/C,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CACvC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,KAAK,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,oBAAY,CAClB,kBAAkB,WAAW,2BAA2B,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QAEL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CACrC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,WAA2B;QACxC,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IACtE,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,aAAa,CAAC,QAAiB;QACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAA;QACzD,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;YACtD,IAAI,CAAC,eAAe;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAClD,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,oBAAY,CAClB,kDAAkD,CACrD,CAAA;QACL,CAAC;QAED,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,MAAM,oBAAoB,GAAG,iKAAiK,MAAM,GAAG,CAAA;YACvM,MAAM,eAAe,GAAoB,MAAM,IAAI,CAAC,KAAK,CACrD,oBAAoB,CACvB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACrD,CAAA;YAED,MAAM,4BAA4B,GAAG,6BAA6B,CAAA;YAClE,MAAM,eAAe,GAAG,mKAAmK,MAAM,GAAG,CAAA;YACpM,MAAM,2BAA2B,GAAG,6BAA6B,CAAA;YAEjE,MAAM,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;YAC9C,MAAM,WAAW,GAAoB,MAAM,IAAI,CAAC,KAAK,CACjD,eAAe,CAClB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CACzD,CAAA;YACD,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAE7C,IAAI,CAAC,0BAA0B;gBAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B;oBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACxC,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,EAAE,CAAA;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACvD,MAAM,cAAc,GAAG,SAAS;aAC3B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;YACf,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,GAC7B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;YAEzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,QAAQ,GAAG,eAAe,CAAA;YAC9B,CAAC;YAED,OAAO,wBAAwB,QAAQ,2BAA2B,IAAI,IAAI,CAAA;QAC9E,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,MAAM,KAAK,GACP,+CAA+C,IAAI,CAAC,UAAU,CAC1D,IAAI,CAAC,2BAA2B,EAAE,CACrC,SAAS;YACV,oKACI,qCAAiB,CAAC,IACtB,KAAK,cAAc,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;QAC1D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACvC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;YAC/B,MAAM,IAAI,GAAG,IAAI,WAAI,EAAE,CAAA;YACvB,MAAM,EAAE,GACJ,MAAM,CAAC,QAAQ,CAAC,KAAK,eAAe;gBAChC,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAClC,MAAM,CAAC,MAAM,CAAC,EACd,SAAS,EACT,EAAE,CACL,CAAA;YACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;YACjC,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEvD,uCAAuC;QACvC,6GAA6G;QAC7G,EAAE;QACF,iBAAiB;QACjB,0FAA0F;QAC1F,uGAAuG;QACvG,6FAA6F;QAC7F,yGAAyG;QAEzG,sFAAsF;QACtF,qFAAqF;QACrF,qEAAqE;QACrE,yFAAyF;QACzF,6FAA6F;QAC7F,4FAA4F;QAC5F,wFAAwF;QACxF,EAAE;QACF,uDAAuD;QACvD,EAAE;QACF,+FAA+F;QAC/F,kFAAkF;QAClF,mEAAmE;QAEnE,MAAM,QAAQ,GAIR,EAAE,CAAA;QAER,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,4DAA4D;YAC5D,MAAM,SAAS,GAAG,mGAAmG,CAAA;YAErH,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;aAAM,CAAC;YACJ,0CAA0C;YAC1C,4CAA4C;YAC5C,yEAAyE;YACzE,MAAM,SAAS,GAAG,UAAU;iBACvB,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC;iBAChC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;gBACf,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,GAC7B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;gBAEzC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,QAAQ,GAAG,eAAe,CAAA;gBAC9B,CAAC;gBAED,OAAO,+HAA+H,QAAQ,2BAA2B,IAAI,GAAG,CAAA;YACpL,CAAC,CAAC;iBACD,IAAI,CAAC,SAAS,CAAC,CAAA;YAEpB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,yDAAyD;QACzD,IAAI,CAAC,QAAQ,CAAC,MAAM;YAAE,OAAO,EAAE,CAAA;QAE/B,0CAA0C;QAC1C,4CAA4C;QAC5C,uEAAuE;QACvE,MAAM,gBAAgB,GAAG,QAAQ;aAC5B,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;YAClC,OAAO;;;;;0CAKmB,YAAY;;wCAEd,UAAU;aACrC,CAAA;QACD,CAAC,CAAC;aACD,IAAI,CAAC,SAAS,CAAC,CAAA;QAEpB,0CAA0C;QAC1C,4CAA4C;QAC5C,uCAAuC;QACvC,MAAM,cAAc,GAAG,QAAQ;aAC1B,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;YAClC,OAAO;;;;;kDAK2B,YAAY;;gDAEd,UAAU;aAC7C,CAAA;QACD,CAAC,CAAC;aACD,IAAI,CAAC,SAAS,CAAC,CAAA;QAEpB,+CAA+C;QAC/C,4CAA4C;QAC5C,uCAAuC;QACvC,MAAM,aAAa,GAAG,QAAQ;aACzB,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;YAClC,OAAO;;;;;+CAKwB,YAAY;;wCAEnB,UAAU;aACrC,CAAA;QACD,CAAC,CAAC;aACD,IAAI,CAAC,SAAS,CAAC,CAAA;QAEpB,0CAA0C;QAC1C,4CAA4C;QAC5C,uCAAuC;QACvC,MAAM,UAAU,GAAG,QAAQ;aACtB,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;YAClC,OAAO;;;;;;0CAMmB,YAAY;;wCAEd,UAAU;iBACjC,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,SAAS,CAAC,CAAA;QAEpB,gDAAgD;QAChD,MAAM,aAAa,GAAG;;;;;;aAMjB,CAAA;QAEL,oCAAoC;QACpC,MAAM,aAAa,GAAG,kBAAkB,cAAc,iDAAiD,CAAA;QAEvG,0CAA0C;QAC1C,MAAM,UAAU,GAAG;;;oBAGP,gBAAgB;yBACX,aAAa;;;;;;;;;aASzB,CAAA;QAEL,qDAAqD;QACrD,MAAM,cAAc,GAAG;;;;;;;;;;;oBAWX,cAAc;0BACR,aAAa;;;;;;;aAO1B,CAAA;QAEL,MAAM,CACF,SAAS,EACT,aAAa,EACb,YAAY,EACZ,SAAS,EACT,aAAa,EAChB,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;SAC7B,CAAC,CAAA;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAA;QACxD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAA;QAErC,kCAAkC;QAClC,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,aAAK,EAAE,CAAA;YAEzB,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,OAAO,CAAC,cAAc,CAAC,CAC3D,CAAA;YACF,MAAM,gBAAgB,GAAG,WAAW,CAAC,WAAW,CAAC,CAAA;YACjD,MAAM,cAAc,GAAG,WAAW,CAAC,SAAS,CAAC,CAAA;YAE7C,qEAAqE;YACrE,MAAM,EAAE,GACJ,OAAO,CAAC,cAAc,CAAC,KAAK,eAAe;gBACvC,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;YACjC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YACxC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CACnC,OAAO,CAAC,YAAY,CAAC,EACrB,SAAS,EACT,EAAE,CACL,CAAA;YAED,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS;iBACJ,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,YAAY,CAAC;gBAClB,OAAO,CAAC,YAAY,CAAC;gBACzB,QAAQ,CAAC,cAAc,CAAC;oBACpB,OAAO,CAAC,cAAc,CAAC,CAClC;iBACA,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CACxC,CAAC,OAAO,EAAE,EAAE;oBACR,OAAO,CACH,OAAO,CAAC,YAAY,CAAC;wBACjB,OAAO,CAAC,YAAY,CAAC;wBACzB,OAAO,CAAC,cAAc,CAAC;4BACnB,OAAO,CAAC,cAAc,CAAC;wBAC3B,OAAO,CAAC,aAAa,CAAC;4BAClB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;4BAC/B,CAAC,CACR,CAAA;gBACL,CAAC,CACJ,CAAA;gBAED,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAChC,CAAC,QAAQ,EAAE,EAAE,CACT,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;oBACxB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAClC,CAAA;gBACL,MAAM,eAAe,GACjB,mBAAmB,CAAC,MAAM,GAAG,CAAC;oBAC9B,aAAa;oBACb,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACjC,OAAO,mBAAmB,CAAC,IAAI,CAC3B,CAAC,WAAW,EAAE,EAAE;4BACZ,OAAO,CACH,KAAK,CAAC,IAAI;gCACN,WAAW,CAAC,YAAY,CAAC;gCAC7B,KAAK,CAAC,WAAW,KAAK,KAAK,CAC9B,CAAA;wBACL,CAAC,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,qBAAqB,GACvB,mBAAmB,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;oBACtC,OAAO,SAAS,CAAC,IAAI,CACjB,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC;wBACjB,WAAW,CAAC,YAAY,CAAC;wBAC7B,OAAO,CAAC,aAAa,CAAC;4BAClB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAC1C,WAAW,CAAC,IAAI;oBACZ,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAA;gBAEvC,qEAAqE;gBACrE,yCAAyC;gBACzC,IAAI,WAAW,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;oBACxC,WAAW,CAAC,IAAI,GAAG,oBAAoB,CAAA;gBAC3C,CAAC;gBAED,WAAW,CAAC,QAAQ;oBAChB,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;wBAC3C,CAAC,CAAC,CAAA;gBACN,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ;oBACvC,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAC3B,UAAU,CACb,KAAK,CAAC,CAAC,CAAA;gBACd,IACI,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CACpC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC,EACV,CAAC;oBACC,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,SAAS,CAC3C,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EACxC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CACvC,CAAA;oBACD,WAAW,CAAC,KAAK;wBACb,KAAK;4BACL,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,KAAK,CAAC,CAClB;4BACG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;4BACjB,CAAC,CAAC,SAAS,CAAA;gBACvB,CAAC;gBAED,IACI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI;oBACnC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,SAAS;oBACxC,CAAC,SAAS;wBACN,QAAQ,CAAC,gBAAgB,CAAC,KAAK,MAAM,CAAC,EAC5C,CAAC;oBACC,WAAW,CAAC,OAAO,GAAG,SAAS,CAAA;gBACnC,CAAC;qBAAM,IACH,mCAAmC,CAAC,IAAI,CACpC,QAAQ,CAAC,gBAAgB,CAAC,CAC7B,EACH,CAAC;oBACC,iFAAiF;oBACjF,iFAAiF;oBACjF,WAAW,CAAC,OAAO;wBACf,QAAQ,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAA;gBAChD,CAAC;qBAAM,IACH,SAAS;oBACT,2BAAY,CAAC,gBAAgB,CACzB,SAAS,EACT,QAAQ,CACX,EACH,CAAC;oBACC,mFAAmF;oBACnF,0EAA0E;oBAC1E,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAA;gBACpD,CAAC;qBAAM,CAAC;oBACJ,WAAW,CAAC,OAAO,GAAG,IAAI,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAA;gBAC3D,CAAC;gBAED,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAChD,iFAAiF;oBACjF,+EAA+E;oBAC/E,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC;yBACnC,SAAS,CACN,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;wBAClC,EAAE,CACT;yBACA,WAAW,EAAE,CAAA;gBACtB,CAAC;gBAED,IAAI,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;oBACpC,WAAW,CAAC,aAAa;wBACrB,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;4BACvC,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,QAAQ,CAAA;oBAElB,0GAA0G;oBAC1G,MAAM,iBAAiB,GACnB,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC;wBAC/B,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;wBAC5B,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;qBACzB,CAAC,CAAA;oBAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC5B,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,CAC/B,CAAA;oBACD,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;wBACjC,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBAC/C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,WAAW,CAAC,QAAQ;oBAChB,mBAAmB,CAAC,MAAM,GAAG,CAAC;wBAC9B,CAAC,eAAe;wBAChB,CAAC,qBAAqB,CAAA;gBAE1B,IAAI,SAAS,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC;oBACzC,wFAAwF;gBAC5F,CAAC;qBAAM,CAAC;oBACJ,WAAW,CAAC,UAAU;wBAClB,QAAQ,CAAC,aAAa,CAAC,KAAK,KAAK,CAAA;gBACzC,CAAC;gBAED,WAAW,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CACtC,CAAC,YAAY,EAAE,EAAE;oBACb,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;wBACtB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,YAAY,CAAC,cAAc,CAAC;4BACxB,QAAQ,CAAC,cAAc,CAAC;wBAC5B,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC,CAC9B,CAAA;gBACL,CAAC,CACJ,CAAA;gBACD,WAAW,CAAC,WAAW;oBACnB,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC;wBAC3C,CAAC,CAAC,CAAA;gBACN,IAAI,WAAW,CAAC,WAAW;oBACvB,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAA;gBAEhD,WAAW,CAAC,OAAO;oBACf,OAAO,QAAQ,CAAC,gBAAgB,CAAC;wBAC7B,QAAQ;wBACZ,QAAQ,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC;wBACnC,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;gBACpC,IAAI,QAAQ,CAAC,oBAAoB,CAAC;oBAC9B,WAAW,CAAC,OAAO;wBACf,QAAQ,CAAC,oBAAoB,CAAC;4BAC9B,cAAc;4BACV,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAA;gBAC5C,IAAI,QAAQ,CAAC,gBAAgB,CAAC;oBAC1B,WAAW,CAAC,SAAS;wBACjB,QAAQ,CAAC,gBAAgB,CAAC;4BAC1B,gBAAgB;4BACZ,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;gBAExC,+CAA+C;gBAC/C,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CACrC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC;oBACR,QAAQ,CAAC,0BAA0B,CAAC,EACtC,CAAC;oBACC,MAAM,MAAM,GACR,QAAQ,CACJ,0BAA0B,CAC7B,CAAC,QAAQ,EAAE,CAAA;oBAChB,WAAW,CAAC,MAAM;wBACd,CAAC,IAAI,CAAC,qBAAqB,CACvB,KAAK,EACL,WAAW,EACX,MAAM,CACT;4BACG,CAAC,CAAC,MAAM;4BACR,CAAC,CAAC,EAAE,CAAA;gBAChB,CAAC;gBAED,IACI,WAAW,CAAC,IAAI,KAAK,SAAS;oBAC9B,WAAW,CAAC,IAAI,KAAK,QAAQ;oBAC7B,WAAW,CAAC,IAAI,KAAK,OAAO,EAC9B,CAAC;oBACC,IACI,QAAQ,CAAC,mBAAmB,CAAC,KAAK,IAAI;wBACtC,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,mBAAmB,CAAC,CAChC;wBAED,WAAW,CAAC,SAAS,GAAG,QAAQ,CAC5B,QAAQ,CAAC,mBAAmB,CAAC,CAChC,CAAA;oBACL,IACI,QAAQ,CAAC,eAAe,CAAC,KAAK,IAAI;wBAClC,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,eAAe,CAAC,CAC5B;wBAED,WAAW,CAAC,KAAK,GAAG,QAAQ,CACxB,QAAQ,CAAC,eAAe,CAAC,CAC5B,CAAA;gBACT,CAAC;gBAED,IACI,WAAW,CAAC,IAAI,KAAK,MAAM;oBAC3B,WAAW,CAAC,IAAI,KAAK,aAAa;oBAClC,WAAW,CAAC,IAAI,KAAK,KAAK,EAC5B,CAAC;oBACC,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;oBACvC,MAAM,KAAK,GAAG,OAAO;yBAChB,SAAS,CACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EACxB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAC3B;yBACA,KAAK,CAAC,GAAG,CAAC,CAAA;oBACf,WAAW,CAAC,IAAI,GAAI,KAAkB,CAAC,GAAG,CACtC,CAAC,IAAI,EAAE,EAAE;wBACL,OAAO,IAAI,CAAC,SAAS,CACjB,CAAC,EACD,IAAI,CAAC,MAAM,GAAG,CAAC,CAClB,CAAA;oBACL,CAAC,CACJ,CAAA;oBACD,WAAW,CAAC,MAAM,GAAG,EAAE,CAAA;gBAC3B,CAAC;gBAED,IACI,CAAC,WAAW,CAAC,IAAI,KAAK,UAAU;oBAC5B,WAAW,CAAC,IAAI,KAAK,MAAM;oBAC3B,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC;oBACrC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI;oBACvC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,SAAS;oBAC5C,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAC3C,EACH,CAAC;oBACC,WAAW,CAAC,SAAS,GAAG,QAAQ,CAC5B,QAAQ,CAAC,oBAAoB,CAAC,CACjC,CAAA;gBACL,CAAC;gBAED,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACT,CAAA;YAED,kGAAkG;YAClG,MAAM,0BAA0B,GAAG,mBAAQ,CAAC,IAAI,CAC5C,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC,CAC9B,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,YAAY,EAAE,EAAE;gBACb,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBAED,mGAAmG;gBACnG,MAAM,QAAQ,GACV,YAAY,CAAC,yBAAyB,CAAC;oBACvC,eAAe;oBACX,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAA;gBACjD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAClD,YAAY,CAAC,uBAAuB,CAAC,EACrC,SAAS,EACT,QAAQ,CACX,CAAA;gBAED,OAAO,IAAI,iCAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;oBACD,kBAAkB,EACd,YAAY,CAAC,yBAAyB,CAAC;oBAC3C,mBAAmB,EAAE,mBAAmB;oBACxC,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC;oBACnC,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC;iBACtC,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,mBAAQ,CAAC,IAAI,CACvC,SAAS,CAAC,MAAM,CACZ,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC;gBAC/C,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,CAAC,cAAc,CAAC,CAC1D,EACD,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CACrC,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACrD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvC,OAAO,CACH,KAAK,CAAC,cAAc,CAAC;wBACjB,UAAU,CAAC,cAAc,CAAC;wBAC9B,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC;wBAChD,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC,CACnD,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAA;gBAExD,OAAO,IAAI,uBAAU,CAAoB;oBACrC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC;oBAC9B,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,QAAQ,EAAE,SAAS,KAAK,CAAC;oBACzB,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC,KAAK,SAAS;oBACjD,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,KAAK,UAAU;iBACtD,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;YAExC,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,iBAA2B;QAC9D,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aACxD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE,CAAA;QAExE,0GAA0G;QAC1G,qEAAqE;QACrE,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACpD,OAAO,CACH,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC9B,CAAC,CAAC,KAAK,CAAC,QAAQ;oBAChB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAChD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,MAAM,uBAAuB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1D,OAAO,CACH,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC/B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CACjD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,kBAAkB,IAAI,CAAC,uBAAuB;gBAC/C,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,uBAAU,CAAC;oBACX,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;oBAC1B,QAAQ,EAAE,IAAI;iBACjB,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,sHAAsH;QACtH,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAClC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CACxC,CAAA;gBACD,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,uBAAU,CAAC;wBACX,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,WAAW,EAAE,MAAM,CAAC,WAAW;wBAC/B,QAAQ,EAAE,IAAI;qBACjB,CAAC,CACL,CAAA;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO;iBAC3B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW;qBAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;qBACxC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBAEL,IAAI,SAAS,GAAG,EAAE,CAAA;gBAClB,IAAI,KAAK,CAAC,QAAQ;oBAAE,SAAS,IAAI,SAAS,CAAA;gBAC1C,IAAI,KAAK,CAAC,SAAS;oBAAE,SAAS,IAAI,UAAU,CAAA;gBAC5C,IAAI,KAAK,CAAC,UAAU;oBAAE,SAAS,IAAI,WAAW,CAAA;gBAC9C,MAAM,WAAW,GACb,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM;oBAC5B,CAAC,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE;oBAChC,CAAC,CAAC,EAAE,CAAA;gBAEZ,OAAO,GAAG,SAAS,WAAW,KAAK,CAAC,IAAI,OAAO,WAAW,IAAI,WAAW,EAAE,CAAA;YAC/E,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,UAAU,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;qBACxC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,KAAK,EACL,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBACL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;qBACxC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,IAAI,UAAU,GAAG,gBACb,EAAE,CAAC,IACP,mBAAmB,WAAW,gBAAgB,IAAI,CAAC,UAAU,CACzD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CACxB,KAAK,qBAAqB,GAAG,CAAA;gBAC9B,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC1D,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAE1D,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc;iBACnC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;iBACrC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,kBAAkB,WAAW,GAAG,CAAA;QAC3C,CAAC;QAED,GAAG,IAAI,YAAY,KAAK,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAA;QAE7C,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAChB,GAAG,IAAI,aAAa,KAAK,CAAC,OAAO,GAAG,CAAA;QACxC,CAAC;QAED,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,WAA2B;QAC9C,OAAO,IAAI,aAAK,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IAClE,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI;iBAC1C,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC3B,QAAQ,EAAE,EAAE,CACpB,CAAA;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACvD,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,qCAAiB,CAAC,IAAI;YAC5B,MAAM,EAAE,eAAe;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,UAAyB;QAC3C,OAAO,IAAI,aAAK,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACnC,UAAyB;QAEzB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACvD,MAAM,QAAQ,GAAG,iCAAe,CAAC,MAAM,CAAC,UAAU,CAAC;YAC/C,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAA;QAChB,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,qCAAiB,CAAC,IAAI;YAC5B,MAAM,EAAE,eAAe;YACvB,IAAI,EAAE,QAAQ;SACjB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;aACxC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,SAAS,GAAG,EAAE,CAAA;QAClB,IAAI,KAAK,CAAC,QAAQ;YAAE,SAAS,IAAI,SAAS,CAAA;QAC1C,IAAI,KAAK,CAAC,SAAS;YAAE,SAAS,IAAI,UAAU,CAAA;QAC5C,IAAI,KAAK,CAAC,UAAU;YAAE,SAAS,IAAI,WAAW,CAAA;QAC9C,MAAM,WAAW,GACb,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM;YAC5B,CAAC,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE;YAChC,CAAC,CAAC,EAAE,CAAA;QAEZ,OAAO,IAAI,aAAK,CACZ,UAAU,SAAS,WAAW,KAAK,CAAC,IAAI,SAAS,IAAI,CAAC,UAAU,CAC5D,KAAK,CACR,KAAK,OAAO,IAAI,WAAW,EAAE,CACjC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,aAAK,CACZ,gBAAgB,SAAS,SAAS,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAC7D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CAAC,KAAY,EAAE,WAAqB;QAC7D,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;aACxC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,iBAAiB,GAAG,CAC7C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,KAAY;QACpC,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAC3D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,UAA2B;QAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;aACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;aAChC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;aACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC;aAChC,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,IAAI,GAAG,GACH,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qBACjC,UAAU,CAAC,IACf,mBAAmB,WAAW,IAAI;YAClC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;QACjC,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QACnE,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QAEnE,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,gBAA0C;QAE1C,MAAM,cAAc,GAAG,iCAAe,CAAC,iBAAiB,CACpD,gBAAgB,CACnB;YACG,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACvB,CAAC,CAAC,gBAAgB,CAAA;QACtB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,cAAc,IAAI,CAC7C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAA;QACf,CAAC;QAED,OAAO,GAAG,OAAO;aACZ,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,mDAAmD;aAC1E,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;aACnB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAEpE,OAAO,IAAI,OAAO,GAAG,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAA6B;QAC9C,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAElE,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO,KAAK,QAAQ,QAAQ,SAAS,IAAI,CAAA;QAC7C,CAAC;QAED,OAAO,KAAK,SAAS,IAAI,CAAA;IAC7B,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,MAAmB,EACnB,WAAoB,EACpB,WAAoB,KAAK;QAEzB,IAAI,CAAC,GAAG,EAAE,CAAA;QACV,IAAI,QAAQ,EAAE,CAAC;YACX,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACrD,CAAC;aAAM,CAAC;YACJ,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAC3D,MAAM,CACT,EAAE,CAAA;QACP,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,CAAC,IAAI,mBAAmB,MAAM,CAAC,OAAO,GAAG,CAAA;QAC7D,IAAI,MAAM,CAAC,SAAS;YAAE,CAAC,IAAI,aAAa,MAAM,CAAC,SAAS,GAAG,CAAA;QAE3D,IAAI,MAAM,CAAC,YAAY;YACnB,CAAC,IAAI,QAAQ,MAAM,CAAC,YAAY,KAC5B,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,SAClD,EAAE,CAAA;QAEN,gHAAgH;QAChH,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC,IAAI,WAAW,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACzB,CAAC,IAAI,WAAW,CAAA;QACpB,CAAC;QACD,IAAI,MAAM,CAAC,IAAI;YACX,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;iBAChB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;iBACrD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;QAEtB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAA;QACxD,IACI,SAAS;YACT,MAAM,CAAC,YAAY;YACnB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,IAAI,SAAS,CAAC,EACnE,CAAC;YACC,yGAAyG;QAC7G,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,CAAC,UAAU;gBAAE,CAAC,IAAI,WAAW,CAAA;YACxC,IAAI,MAAM,CAAC,UAAU;gBAAE,CAAC,IAAI,OAAO,CAAA;QACvC,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,WAAW;YAAE,CAAC,IAAI,cAAc,CAAA;QACzD,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,kBAAkB,KAAK,WAAW;YAC/D,8FAA8F;YAC9F,CAAC,IAAI,iBAAiB,CAAA;QAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;YAC3C,CAAC,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAA;QACzD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI;YACvD,CAAC,IAAI,YAAY,MAAM,CAAC,OAAO,EAAE,CAAA;QACrC,IAAI,MAAM,CAAC,QAAQ;YAAE,CAAC,IAAI,cAAc,MAAM,CAAC,QAAQ,EAAE,CAAA;QAEzD,OAAO,CAAC,CAAA;IACZ,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,MAAM,GAA0B,MAAM,IAAI,CAAC,KAAK,CAClD,iCAAiC,CACpC,CAAA;QAED,8CAA8C;QAC9C,+CAA+C;QAC/C,6FAA6F;QAC7F,YAAY;QACZ,qBAAqB;QACrB,MAAM,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QAEvC,OAAO,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IACtD,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,KAAY,EACZ,MAAmB,EACnB,KAAa;QAEb,6EAA6E;QAC7E,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACxD,MAAM,cAAc,GAAG,QAAQ,CAAC,0BAA0B,CACtD,MAAM,CAAC,IAAI,CACd,CAAA;YACD,IAAI,cAAc,IAAI,cAAc,CAAC,KAAK;gBAAE,OAAO,KAAK,CAAA;QAC5D,CAAC;QAED,MAAM,mBAAmB,GACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB;YACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAA;QAE9D,IAAI,mBAAmB,EAAE,CAAC;YACtB,sFAAsF;YACtF,kDAAkD;YAClD,MAAM,+BAA+B,GAAG;gBACpC,KAAK;gBACL,SAAS;gBACT,UAAU;gBACV,WAAW;aACd,CAAA;YACD,MAAM,eAAe,GACjB,+BAA+B,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;YAC/D,IAAI,MAAM,CAAC,QAAQ,IAAI,eAAe,EAAE,CAAC;gBACrC,OAAO,mBAAmB,GAAG,CAAC,KAAK,KAAK,CAAA;YAC5C,CAAC;iBAAM,CAAC;gBACJ,OAAO,mBAAmB,KAAK,KAAK,CAAA;YACxC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;CACJ;AAp0GD,4CAo0GC", "file": "MysqlQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TypeORMError } from \"../../error\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { TableIndexOptions } from \"../../schema-builder/options/TableIndexOptions\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { VersionUtils } from \"../../util/VersionUtils\"\nimport { Query } from \"../Query\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { MysqlDriver } from \"./MysqlDriver\"\n\n/**\n * Runs queries on a single mysql database connection.\n */\nexport class MysqlQueryRunner extends BaseQueryRunner implements QueryRunner {\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: MysqlDriver\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Promise used to obtain a database connection from a pool for a first time.\n     */\n    protected databaseConnectionPromise: Promise<any>\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: MysqlDriver, mode: ReplicationMode) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n        this.mode = mode\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<any> {\n        if (this.databaseConnection)\n            return Promise.resolve(this.databaseConnection)\n\n        if (this.databaseConnectionPromise)\n            return this.databaseConnectionPromise\n\n        if (this.mode === \"slave\" && this.driver.isReplicated) {\n            this.databaseConnectionPromise = this.driver\n                .obtainSlaveConnection()\n                .then((connection) => {\n                    this.databaseConnection = connection\n                    return this.databaseConnection\n                })\n        } else {\n            // master\n            this.databaseConnectionPromise = this.driver\n                .obtainMasterConnection()\n                .then((connection) => {\n                    this.databaseConnection = connection\n                    return this.databaseConnection\n                })\n        }\n\n        return this.databaseConnectionPromise\n    }\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods once its released.\n     */\n    release(): Promise<void> {\n        this.isReleased = true\n        if (this.databaseConnection) this.databaseConnection.release()\n        return Promise.resolve()\n    }\n\n    /**\n     * Starts transaction on the current connection.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n        if (this.transactionDepth === 0) {\n            if (isolationLevel) {\n                await this.query(\n                    \"SET TRANSACTION ISOLATION LEVEL \" + isolationLevel,\n                )\n            }\n            await this.query(\"START TRANSACTION\")\n        } else {\n            await this.query(`SAVEPOINT typeorm_${this.transactionDepth}`)\n        }\n        this.transactionDepth += 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `RELEASE SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.query(\"COMMIT\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `ROLLBACK TO SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.query(\"ROLLBACK\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Executes a raw SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n        const queryStartTime = Date.now()\n\n        return new Promise(async (ok, fail) => {\n            try {\n                const enableQueryTimeout =\n                    this.driver.options.enableQueryTimeout\n                const maxQueryExecutionTime =\n                    this.driver.options.maxQueryExecutionTime\n                const queryPayload =\n                    enableQueryTimeout && maxQueryExecutionTime\n                        ? { sql: query, timeout: maxQueryExecutionTime }\n                        : query\n                databaseConnection.query(\n                    queryPayload,\n                    parameters,\n                    async (err: any, raw: any) => {\n                        // log slow queries if maxQueryExecution time is set\n                        const maxQueryExecutionTime =\n                            this.driver.options.maxQueryExecutionTime\n                        const queryEndTime = Date.now()\n                        const queryExecutionTime = queryEndTime - queryStartTime\n\n                        if (\n                            maxQueryExecutionTime &&\n                            queryExecutionTime > maxQueryExecutionTime\n                        )\n                            this.driver.connection.logger.logQuerySlow(\n                                queryExecutionTime,\n                                query,\n                                parameters,\n                                this,\n                            )\n\n                        if (err) {\n                            this.driver.connection.logger.logQueryError(\n                                err,\n                                query,\n                                parameters,\n                                this,\n                            )\n                            this.broadcaster.broadcastAfterQueryEvent(\n                                broadcasterResult,\n                                query,\n                                parameters,\n                                false,\n                                undefined,\n                                undefined,\n                                err,\n                            )\n\n                            return fail(\n                                new QueryFailedError(query, parameters, err),\n                            )\n                        }\n\n                        this.broadcaster.broadcastAfterQueryEvent(\n                            broadcasterResult,\n                            query,\n                            parameters,\n                            true,\n                            queryExecutionTime,\n                            raw,\n                            undefined,\n                        )\n\n                        const result = new QueryResult()\n\n                        result.raw = raw\n\n                        try {\n                            result.records = Array.from(raw)\n                        } catch {\n                            // Do nothing.\n                        }\n\n                        if (raw?.hasOwnProperty(\"affectedRows\")) {\n                            result.affected = raw.affectedRows\n                        }\n\n                        if (useStructuredResult) {\n                            ok(result)\n                        } else {\n                            ok(result.raw)\n                        }\n                    },\n                )\n            } catch (err) {\n                fail(err)\n            } finally {\n                await broadcasterResult.wait()\n            }\n        })\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        return new Promise(async (ok, fail) => {\n            try {\n                const databaseConnection = await this.connect()\n                this.driver.connection.logger.logQuery(query, parameters, this)\n                const databaseQuery = databaseConnection.query(\n                    query,\n                    parameters,\n                )\n                if (onEnd) databaseQuery.on(\"end\", onEnd)\n                if (onError) databaseQuery.on(\"error\", onError)\n                ok(databaseQuery.stream())\n            } catch (err) {\n                fail(err)\n            }\n        })\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        throw new TypeORMError(`MySql driver does not support table schemas`)\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT * FROM \\`INFORMATION_SCHEMA\\`.\\`SCHEMATA\\` WHERE \\`SCHEMA_NAME\\` = '${database}'`,\n        )\n        return result.length ? true : false\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<string> {\n        const query = await this.query(`SELECT DATABASE() AS \\`db_name\\``)\n        return query[0][\"db_name\"]\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        throw new TypeORMError(`MySql driver does not support table schemas`)\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<string> {\n        const query = await this.query(`SELECT SCHEMA() AS \\`schema_name\\``)\n        return query[0][\"schema_name\"]\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n        const sql = `SELECT * FROM \\`INFORMATION_SCHEMA\\`.\\`COLUMNS\\` WHERE \\`TABLE_SCHEMA\\` = '${parsedTableName.database}' AND \\`TABLE_NAME\\` = '${parsedTableName.tableName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        column: TableColumn | string,\n    ): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n        const columnName = InstanceChecker.isTableColumn(column)\n            ? column.name\n            : column\n        const sql = `SELECT * FROM \\`INFORMATION_SCHEMA\\`.\\`COLUMNS\\` WHERE \\`TABLE_SCHEMA\\` = '${parsedTableName.database}' AND \\`TABLE_NAME\\` = '${parsedTableName.tableName}' AND \\`COLUMN_NAME\\` = '${columnName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Creates a new database.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        const up = ifNotExist\n            ? `CREATE DATABASE IF NOT EXISTS \\`${database}\\``\n            : `CREATE DATABASE \\`${database}\\``\n        const down = `DROP DATABASE \\`${database}\\``\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Drops database.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        const up = ifExist\n            ? `DROP DATABASE IF EXISTS \\`${database}\\``\n            : `DROP DATABASE \\`${database}\\``\n        const down = `CREATE DATABASE \\`${database}\\``\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema create queries are not supported by MySql driver.`,\n        )\n    }\n\n    /**\n     * Drops table schema.\n     */\n    async dropSchema(schemaPath: string, ifExist?: boolean): Promise<void> {\n        throw new TypeORMError(\n            `Schema drop queries are not supported by MySql driver.`,\n        )\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        // we must first drop indices, than drop foreign keys, because drop queries runs in reversed order\n        // and foreign keys will be dropped first as indices. This order is very important, because we can't drop index\n        // if it related to the foreign key.\n\n        // createTable does not need separate method to create indices, because it create indices in the same query with table creation.\n        table.indices.forEach((index) =>\n            downQueries.push(this.dropIndexSql(table, index)),\n        )\n\n        // if createForeignKeys is true, we must drop created foreign keys in down query.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (createForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                downQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        // if table has column with generated type, we must add the expression to the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const currentDatabase = await this.getCurrentDatabase()\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        return this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drop the table.\n     */\n    async dropTable(\n        target: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n    ): Promise<void> {\n        // It needs because if table does not exist and dropForeignKeys or dropIndices is true, we don't need\n        // to perform drop queries for foreign keys and indices.\n        if (ifExist) {\n            const isTableExist = await this.hasTable(target)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const tablePath = this.getTablePath(target)\n        const table = await this.getCachedTable(tablePath)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (dropForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                upQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        table.indices.forEach((index) =>\n            upQueries.push(this.dropIndexSql(table, index)),\n        )\n\n        upQueries.push(this.dropTableSql(table))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        // if table had columns with generated type, we must remove the expression from the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const currentDatabase = await this.getCurrentDatabase()\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(\n        view: View,\n        syncWithMetadata: boolean = false,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        if (syncWithMetadata)\n            upQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        if (syncWithMetadata)\n            downQueries.push(await this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = InstanceChecker.isView(target) ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(await this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames a table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        const oldTable = InstanceChecker.isTable(oldTableOrName)\n            ? oldTableOrName\n            : await this.getCachedTable(oldTableOrName)\n        const newTable = oldTable.clone()\n\n        const { database } = this.driver.parseTableName(oldTable)\n\n        newTable.name = database ? `${database}.${newTableName}` : newTableName\n\n        // rename table\n        upQueries.push(\n            new Query(\n                `RENAME TABLE ${this.escapePath(oldTable)} TO ${this.escapePath(\n                    newTable,\n                )}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `RENAME TABLE ${this.escapePath(newTable)} TO ${this.escapePath(\n                    oldTable,\n                )}`,\n            ),\n        )\n\n        // rename index constraints\n        newTable.indices.forEach((index) => {\n            const oldIndexName = this.connection.namingStrategy.indexName(\n                oldTable,\n                index.columnNames,\n            )\n\n            // Skip renaming if Index has user defined constraint name\n            if (index.name !== oldIndexName) return\n\n            // build new constraint name\n            const columnNames = index.columnNames\n                .map((column) => `\\`${column}\\``)\n                .join(\", \")\n            const newIndexName = this.connection.namingStrategy.indexName(\n                newTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // build queries\n            let indexType = \"\"\n            if (index.isUnique) indexType += \"UNIQUE \"\n            if (index.isSpatial) indexType += \"SPATIAL \"\n            if (index.isFulltext) indexType += \"FULLTEXT \"\n            const indexParser =\n                index.isFulltext && index.parser\n                    ? ` WITH PARSER ${index.parser}`\n                    : \"\"\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(newTable)} DROP INDEX \\`${\n                        index.name\n                    }\\`, ADD ${indexType}INDEX \\`${newIndexName}\\` (${columnNames})${indexParser}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} DROP INDEX \\`${newIndexName}\\`, ADD ${indexType}INDEX \\`${\n                        index.name\n                    }\\` (${columnNames})${indexParser}`,\n                ),\n            )\n\n            // replace constraint name\n            index.name = newIndexName\n        })\n\n        // rename foreign key constraint\n        newTable.foreignKeys.forEach((foreignKey) => {\n            const oldForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    oldTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // Skip renaming if foreign key has user defined constraint name\n            if (foreignKey.name !== oldForeignKeyName) return\n\n            // build new constraint name\n            const columnNames = foreignKey.columnNames\n                .map((column) => `\\`${column}\\``)\n                .join(\", \")\n            const referencedColumnNames = foreignKey.referencedColumnNames\n                .map((column) => `\\`${column}\\``)\n                .join(\",\")\n            const newForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    newTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // build queries\n            let up =\n                `ALTER TABLE ${this.escapePath(newTable)} DROP FOREIGN KEY \\`${\n                    foreignKey.name\n                }\\`, ADD CONSTRAINT \\`${newForeignKeyName}\\` FOREIGN KEY (${columnNames}) ` +\n                `REFERENCES ${this.escapePath(\n                    this.getTablePath(foreignKey),\n                )}(${referencedColumnNames})`\n            if (foreignKey.onDelete) up += ` ON DELETE ${foreignKey.onDelete}`\n            if (foreignKey.onUpdate) up += ` ON UPDATE ${foreignKey.onUpdate}`\n\n            let down =\n                `ALTER TABLE ${this.escapePath(\n                    newTable,\n                )} DROP FOREIGN KEY \\`${newForeignKeyName}\\`, ADD CONSTRAINT \\`${\n                    foreignKey.name\n                }\\` FOREIGN KEY (${columnNames}) ` +\n                `REFERENCES ${this.escapePath(\n                    this.getTablePath(foreignKey),\n                )}(${referencedColumnNames})`\n            if (foreignKey.onDelete) down += ` ON DELETE ${foreignKey.onDelete}`\n            if (foreignKey.onUpdate) down += ` ON UPDATE ${foreignKey.onUpdate}`\n\n            upQueries.push(new Query(up))\n            downQueries.push(new Query(down))\n\n            // replace constraint name\n            foreignKey.name = newForeignKeyName\n        })\n\n        await this.executeQueries(upQueries, downQueries)\n\n        // rename old table and replace it in cached tabled;\n        oldTable.name = newTable.name\n        this.replaceCachedTable(oldTable, newTable)\n    }\n\n    /**\n     * Change table comment.\n     */\n    async changeTableComment(\n        tableOrName: Table | string,\n        newComment?: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        newComment = this.escapeComment(newComment)\n        const comment = this.escapeComment(table.comment)\n\n        if (newComment === comment) {\n            return\n        }\n\n        const newTable = table.clone()\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    newTable,\n                )} COMMENT ${newComment}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} COMMENT ${comment}`,\n            ),\n        )\n\n        await this.executeQueries(upQueries, downQueries)\n\n        // change table comment and replace it in cached tabled;\n        table.comment = newTable.comment\n        this.replaceCachedTable(table, newTable)\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        const skipColumnLevelPrimary = clonedTable.primaryColumns.length > 0\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(\n                    column,\n                    skipColumnLevelPrimary,\n                    false,\n                )}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \\`${\n                    column.name\n                }\\``,\n            ),\n        )\n\n        // create or update primary key constraint\n        if (column.isPrimary && skipColumnLevelPrimary) {\n            // if we already have generated column, we must temporary drop AUTO_INCREMENT property.\n            const generatedColumn = clonedTable.columns.find(\n                (column) =>\n                    column.isGenerated &&\n                    column.generationStrategy === \"increment\",\n            )\n            if (generatedColumn) {\n                const nonGeneratedColumn = generatedColumn.clone()\n                nonGeneratedColumn.isGenerated = false\n                nonGeneratedColumn.generationStrategy = undefined\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            column.name\n                        }\\` ${this.buildCreateColumnSql(\n                            nonGeneratedColumn,\n                            true,\n                        )}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            nonGeneratedColumn.name\n                        }\\` ${this.buildCreateColumnSql(column, true)}`,\n                    ),\n                )\n            }\n\n            const primaryColumns = clonedTable.primaryColumns\n            let columnNames = primaryColumns\n                .map((column) => `\\`${column.name}\\``)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} DROP PRIMARY KEY`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD PRIMARY KEY (${columnNames})`,\n                ),\n            )\n\n            primaryColumns.push(column)\n            columnNames = primaryColumns\n                .map((column) => `\\`${column.name}\\``)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD PRIMARY KEY (${columnNames})`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} DROP PRIMARY KEY`,\n                ),\n            )\n\n            // if we previously dropped AUTO_INCREMENT property, we must bring it back\n            if (generatedColumn) {\n                const nonGeneratedColumn = generatedColumn.clone()\n                nonGeneratedColumn.isGenerated = false\n                nonGeneratedColumn.generationStrategy = undefined\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            nonGeneratedColumn.name\n                        }\\` ${this.buildCreateColumnSql(column, true)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            column.name\n                        }\\` ${this.buildCreateColumnSql(\n                            nonGeneratedColumn,\n                            true,\n                        )}`,\n                    ),\n                )\n            }\n        }\n\n        if (column.generatedType && column.asExpression) {\n            const currentDatabase = await this.getCurrentDatabase()\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        // create column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            upQueries.push(this.createIndexSql(table, columnIndex))\n            downQueries.push(this.dropIndexSql(table, columnIndex))\n        } else if (column.isUnique) {\n            const uniqueIndex = new TableIndex({\n                name: this.connection.namingStrategy.indexName(table, [\n                    column.name,\n                ]),\n                columnNames: [column.name],\n                isUnique: true,\n            })\n            clonedTable.indices.push(uniqueIndex)\n            clonedTable.uniques.push(\n                new TableUnique({\n                    name: uniqueIndex.name,\n                    columnNames: uniqueIndex.columnNames,\n                }),\n            )\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} ADD UNIQUE INDEX \\`${\n                        uniqueIndex.name\n                    }\\` (\\`${column.name}\\`)`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} DROP INDEX \\`${\n                        uniqueIndex.name\n                    }\\``,\n                ),\n            )\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.addColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.addColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        let newColumn: TableColumn | undefined = undefined\n        if (InstanceChecker.isTableColumn(newTableColumnOrName)) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        await this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        let clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        const oldColumn = InstanceChecker.isTableColumn(oldColumnOrName)\n            ? oldColumnOrName\n            : table.columns.find((column) => column.name === oldColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        if (\n            (newColumn.isGenerated !== oldColumn.isGenerated &&\n                newColumn.generationStrategy !== \"uuid\") ||\n            oldColumn.type !== newColumn.type ||\n            oldColumn.length !== newColumn.length ||\n            (oldColumn.generatedType &&\n                newColumn.generatedType &&\n                oldColumn.generatedType !== newColumn.generatedType) ||\n            (!oldColumn.generatedType &&\n                newColumn.generatedType === \"VIRTUAL\") ||\n            (oldColumn.generatedType === \"VIRTUAL\" && !newColumn.generatedType)\n        ) {\n            await this.dropColumn(table, oldColumn)\n            await this.addColumn(table, newColumn)\n\n            // update cloned table\n            clonedTable = table.clone()\n        } else {\n            if (newColumn.name !== oldColumn.name) {\n                // We don't change any column properties, just rename it.\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            oldColumn.name\n                        }\\` \\`${newColumn.name}\\` ${this.buildCreateColumnSql(\n                            oldColumn,\n                            true,\n                            true,\n                        )}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            newColumn.name\n                        }\\` \\`${oldColumn.name}\\` ${this.buildCreateColumnSql(\n                            oldColumn,\n                            true,\n                            true,\n                        )}`,\n                    ),\n                )\n\n                // rename index constraints\n                clonedTable.findColumnIndices(oldColumn).forEach((index) => {\n                    const oldUniqueName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                        )\n\n                    // Skip renaming if Index has user defined constraint name\n                    if (index.name !== oldUniqueName) return\n\n                    // build new constraint name\n                    index.columnNames.splice(\n                        index.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    index.columnNames.push(newColumn.name)\n                    const columnNames = index.columnNames\n                        .map((column) => `\\`${column}\\``)\n                        .join(\", \")\n                    const newIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // build queries\n                    let indexType = \"\"\n                    if (index.isUnique) indexType += \"UNIQUE \"\n                    if (index.isSpatial) indexType += \"SPATIAL \"\n                    if (index.isFulltext) indexType += \"FULLTEXT \"\n                    const indexParser =\n                        index.isFulltext && index.parser\n                            ? ` WITH PARSER ${index.parser}`\n                            : \"\"\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP INDEX \\`${\n                                index.name\n                            }\\`, ADD ${indexType}INDEX \\`${newIndexName}\\` (${columnNames})${indexParser}`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP INDEX \\`${newIndexName}\\`, ADD ${indexType}INDEX \\`${\n                                index.name\n                            }\\` (${columnNames})${indexParser}`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    index.name = newIndexName\n                })\n\n                // rename foreign key constraints\n                clonedTable\n                    .findColumnForeignKeys(oldColumn)\n                    .forEach((foreignKey) => {\n                        const foreignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // Skip renaming if foreign key has user defined constraint name\n                        if (foreignKey.name !== foreignKeyName) return\n\n                        // build new constraint name\n                        foreignKey.columnNames.splice(\n                            foreignKey.columnNames.indexOf(oldColumn.name),\n                            1,\n                        )\n                        foreignKey.columnNames.push(newColumn.name)\n                        const columnNames = foreignKey.columnNames\n                            .map((column) => `\\`${column}\\``)\n                            .join(\", \")\n                        const referencedColumnNames =\n                            foreignKey.referencedColumnNames\n                                .map((column) => `\\`${column}\\``)\n                                .join(\",\")\n                        const newForeignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // build queries\n                        let up =\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP FOREIGN KEY \\`${\n                                foreignKey.name\n                            }\\`, ADD CONSTRAINT \\`${newForeignKeyName}\\` FOREIGN KEY (${columnNames}) ` +\n                            `REFERENCES ${this.escapePath(\n                                this.getTablePath(foreignKey),\n                            )}(${referencedColumnNames})`\n                        if (foreignKey.onDelete)\n                            up += ` ON DELETE ${foreignKey.onDelete}`\n                        if (foreignKey.onUpdate)\n                            up += ` ON UPDATE ${foreignKey.onUpdate}`\n\n                        let down =\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP FOREIGN KEY \\`${newForeignKeyName}\\`, ADD CONSTRAINT \\`${\n                                foreignKey.name\n                            }\\` FOREIGN KEY (${columnNames}) ` +\n                            `REFERENCES ${this.escapePath(\n                                this.getTablePath(foreignKey),\n                            )}(${referencedColumnNames})`\n                        if (foreignKey.onDelete)\n                            down += ` ON DELETE ${foreignKey.onDelete}`\n                        if (foreignKey.onUpdate)\n                            down += ` ON UPDATE ${foreignKey.onUpdate}`\n\n                        upQueries.push(new Query(up))\n                        downQueries.push(new Query(down))\n\n                        // replace constraint name\n                        foreignKey.name = newForeignKeyName\n                    })\n\n                // rename old column in the Table object\n                const oldTableColumn = clonedTable.columns.find(\n                    (column) => column.name === oldColumn.name,\n                )\n                clonedTable.columns[\n                    clonedTable.columns.indexOf(oldTableColumn!)\n                ].name = newColumn.name\n                oldColumn.name = newColumn.name\n            }\n\n            if (this.isColumnChanged(oldColumn, newColumn, true, true)) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            oldColumn.name\n                        }\\` ${this.buildCreateColumnSql(newColumn, true)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            newColumn.name\n                        }\\` ${this.buildCreateColumnSql(oldColumn, true)}`,\n                    ),\n                )\n\n                if (oldColumn.generatedType && !newColumn.generatedType) {\n                    // if column changed from generated to non-generated, delete record from typeorm metadata\n\n                    const currentDatabase = await this.getCurrentDatabase()\n                    const deleteQuery = this.deleteTypeormMetadataSql({\n                        schema: currentDatabase,\n                        table: table.name,\n                        type: MetadataTableType.GENERATED_COLUMN,\n                        name: oldColumn.name,\n                    })\n                    const insertQuery = this.insertTypeormMetadataSql({\n                        schema: currentDatabase,\n                        table: table.name,\n                        type: MetadataTableType.GENERATED_COLUMN,\n                        name: oldColumn.name,\n                        value: oldColumn.asExpression,\n                    })\n\n                    upQueries.push(deleteQuery)\n                    downQueries.push(insertQuery)\n                } else if (\n                    !oldColumn.generatedType &&\n                    newColumn.generatedType\n                ) {\n                    // if column changed from non-generated to generated, insert record into typeorm metadata\n\n                    const currentDatabase = await this.getCurrentDatabase()\n                    const insertQuery = this.insertTypeormMetadataSql({\n                        schema: currentDatabase,\n                        table: table.name,\n                        type: MetadataTableType.GENERATED_COLUMN,\n                        name: newColumn.name,\n                        value: newColumn.asExpression,\n                    })\n                    const deleteQuery = this.deleteTypeormMetadataSql({\n                        schema: currentDatabase,\n                        table: table.name,\n                        type: MetadataTableType.GENERATED_COLUMN,\n                        name: newColumn.name,\n                    })\n\n                    upQueries.push(insertQuery)\n                    downQueries.push(deleteQuery)\n                } else if (oldColumn.asExpression !== newColumn.asExpression) {\n                    // if only expression changed, just update it in typeorm_metadata table\n                    const currentDatabase = await this.getCurrentDatabase()\n                    const updateQuery = this.connection\n                        .createQueryBuilder()\n                        .update(this.getTypeormMetadataTableName())\n                        .set({ value: newColumn.asExpression })\n                        .where(\"`type` = :type\", {\n                            type: MetadataTableType.GENERATED_COLUMN,\n                        })\n                        .andWhere(\"`name` = :name\", { name: oldColumn.name })\n                        .andWhere(\"`schema` = :schema\", {\n                            schema: currentDatabase,\n                        })\n                        .andWhere(\"`table` = :table\", { table: table.name })\n                        .getQueryAndParameters()\n\n                    const revertUpdateQuery = this.connection\n                        .createQueryBuilder()\n                        .update(this.getTypeormMetadataTableName())\n                        .set({ value: oldColumn.asExpression })\n                        .where(\"`type` = :type\", {\n                            type: MetadataTableType.GENERATED_COLUMN,\n                        })\n                        .andWhere(\"`name` = :name\", { name: newColumn.name })\n                        .andWhere(\"`schema` = :schema\", {\n                            schema: currentDatabase,\n                        })\n                        .andWhere(\"`table` = :table\", { table: table.name })\n                        .getQueryAndParameters()\n\n                    upQueries.push(new Query(updateQuery[0], updateQuery[1]))\n                    downQueries.push(\n                        new Query(revertUpdateQuery[0], revertUpdateQuery[1]),\n                    )\n                }\n            }\n\n            if (newColumn.isPrimary !== oldColumn.isPrimary) {\n                // if table have generated column, we must drop AUTO_INCREMENT before changing primary constraints.\n                const generatedColumn = clonedTable.columns.find(\n                    (column) =>\n                        column.isGenerated &&\n                        column.generationStrategy === \"increment\",\n                )\n                if (generatedColumn) {\n                    const nonGeneratedColumn = generatedColumn.clone()\n                    nonGeneratedColumn.isGenerated = false\n                    nonGeneratedColumn.generationStrategy = undefined\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                                generatedColumn.name\n                            }\\` ${this.buildCreateColumnSql(\n                                nonGeneratedColumn,\n                                true,\n                            )}`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                                nonGeneratedColumn.name\n                            }\\` ${this.buildCreateColumnSql(\n                                generatedColumn,\n                                true,\n                            )}`,\n                        ),\n                    )\n                }\n\n                const primaryColumns = clonedTable.primaryColumns\n\n                // if primary column state changed, we must always drop existed constraint.\n                if (primaryColumns.length > 0) {\n                    const columnNames = primaryColumns\n                        .map((column) => `\\`${column.name}\\``)\n                        .join(\", \")\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP PRIMARY KEY`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                }\n\n                if (newColumn.isPrimary === true) {\n                    primaryColumns.push(newColumn)\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = true\n                    const columnNames = primaryColumns\n                        .map((column) => `\\`${column.name}\\``)\n                        .join(\", \")\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP PRIMARY KEY`,\n                        ),\n                    )\n                } else {\n                    const primaryColumn = primaryColumns.find(\n                        (c) => c.name === newColumn.name,\n                    )\n                    primaryColumns.splice(\n                        primaryColumns.indexOf(primaryColumn!),\n                        1,\n                    )\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = false\n\n                    // if we have another primary keys, we must recreate constraint.\n                    if (primaryColumns.length > 0) {\n                        const columnNames = primaryColumns\n                            .map((column) => `\\`${column.name}\\``)\n                            .join(\", \")\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ADD PRIMARY KEY (${columnNames})`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} DROP PRIMARY KEY`,\n                            ),\n                        )\n                    }\n                }\n\n                // if we have generated column, and we dropped AUTO_INCREMENT property before, we must bring it back\n                if (generatedColumn) {\n                    const nonGeneratedColumn = generatedColumn.clone()\n                    nonGeneratedColumn.isGenerated = false\n                    nonGeneratedColumn.generationStrategy = undefined\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                                nonGeneratedColumn.name\n                            }\\` ${this.buildCreateColumnSql(\n                                generatedColumn,\n                                true,\n                            )}`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                                generatedColumn.name\n                            }\\` ${this.buildCreateColumnSql(\n                                nonGeneratedColumn,\n                                true,\n                            )}`,\n                        ),\n                    )\n                }\n            }\n\n            if (newColumn.isUnique !== oldColumn.isUnique) {\n                if (newColumn.isUnique === true) {\n                    const uniqueIndex = new TableIndex({\n                        name: this.connection.namingStrategy.indexName(table, [\n                            newColumn.name,\n                        ]),\n                        columnNames: [newColumn.name],\n                        isUnique: true,\n                    })\n                    clonedTable.indices.push(uniqueIndex)\n                    clonedTable.uniques.push(\n                        new TableUnique({\n                            name: uniqueIndex.name,\n                            columnNames: uniqueIndex.columnNames,\n                        }),\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD UNIQUE INDEX \\`${uniqueIndex.name}\\` (\\`${\n                                newColumn.name\n                            }\\`)`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP INDEX \\`${uniqueIndex.name}\\``,\n                        ),\n                    )\n                } else {\n                    const uniqueIndex = clonedTable.indices.find((index) => {\n                        return (\n                            index.columnNames.length === 1 &&\n                            index.isUnique === true &&\n                            !!index.columnNames.find(\n                                (columnName) => columnName === newColumn.name,\n                            )\n                        )\n                    })\n                    clonedTable.indices.splice(\n                        clonedTable.indices.indexOf(uniqueIndex!),\n                        1,\n                    )\n\n                    const tableUnique = clonedTable.uniques.find(\n                        (unique) => unique.name === uniqueIndex!.name,\n                    )\n                    clonedTable.uniques.splice(\n                        clonedTable.uniques.indexOf(tableUnique!),\n                        1,\n                    )\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP INDEX \\`${uniqueIndex!.name}\\``,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD UNIQUE INDEX \\`${uniqueIndex!.name}\\` (\\`${\n                                newColumn.name\n                            }\\`)`,\n                        ),\n                    )\n                }\n            }\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        for (const { oldColumn, newColumn } of changedColumns) {\n            await this.changeColumn(tableOrName, oldColumn, newColumn)\n        }\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const column = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName\n            : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table \"${table.name}\"`,\n            )\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop primary key constraint\n        if (column.isPrimary) {\n            // if table have generated column, we must drop AUTO_INCREMENT before changing primary constraints.\n            const generatedColumn = clonedTable.columns.find(\n                (column) =>\n                    column.isGenerated &&\n                    column.generationStrategy === \"increment\",\n            )\n            if (generatedColumn) {\n                const nonGeneratedColumn = generatedColumn.clone()\n                nonGeneratedColumn.isGenerated = false\n                nonGeneratedColumn.generationStrategy = undefined\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            generatedColumn.name\n                        }\\` ${this.buildCreateColumnSql(\n                            nonGeneratedColumn,\n                            true,\n                        )}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            nonGeneratedColumn.name\n                        }\\` ${this.buildCreateColumnSql(\n                            generatedColumn,\n                            true,\n                        )}`,\n                    ),\n                )\n            }\n\n            // dropping primary key constraint\n            const columnNames = clonedTable.primaryColumns\n                .map((primaryColumn) => `\\`${primaryColumn.name}\\``)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} DROP PRIMARY KEY`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} ADD PRIMARY KEY (${columnNames})`,\n                ),\n            )\n\n            // update column in table\n            const tableColumn = clonedTable.findColumnByName(column.name)\n            tableColumn!.isPrimary = false\n\n            // if primary key have multiple columns, we must recreate it without dropped column\n            if (clonedTable.primaryColumns.length > 0) {\n                const columnNames = clonedTable.primaryColumns\n                    .map((primaryColumn) => `\\`${primaryColumn.name}\\``)\n                    .join(\", \")\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} ADD PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} DROP PRIMARY KEY`,\n                    ),\n                )\n            }\n\n            // if we have generated column, and we dropped AUTO_INCREMENT property before, and this column is not current dropping column, we must bring it back\n            if (generatedColumn && generatedColumn.name !== column.name) {\n                const nonGeneratedColumn = generatedColumn.clone()\n                nonGeneratedColumn.isGenerated = false\n                nonGeneratedColumn.generationStrategy = undefined\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            nonGeneratedColumn.name\n                        }\\` ${this.buildCreateColumnSql(\n                            generatedColumn,\n                            true,\n                        )}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                            generatedColumn.name\n                        }\\` ${this.buildCreateColumnSql(\n                            nonGeneratedColumn,\n                            true,\n                        )}`,\n                    ),\n                )\n            }\n        }\n\n        // drop column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            clonedTable.indices.splice(\n                clonedTable.indices.indexOf(columnIndex),\n                1,\n            )\n            upQueries.push(this.dropIndexSql(table, columnIndex))\n            downQueries.push(this.createIndexSql(table, columnIndex))\n        } else if (column.isUnique) {\n            // we splice constraints both from table uniques and indices.\n            const uniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(table, [\n                    column.name,\n                ])\n            const foundUnique = clonedTable.uniques.find(\n                (unique) => unique.name === uniqueName,\n            )\n            if (foundUnique)\n                clonedTable.uniques.splice(\n                    clonedTable.uniques.indexOf(foundUnique),\n                    1,\n                )\n\n            const indexName = this.connection.namingStrategy.indexName(table, [\n                column.name,\n            ])\n            const foundIndex = clonedTable.indices.find(\n                (index) => index.name === indexName,\n            )\n            if (foundIndex)\n                clonedTable.indices.splice(\n                    clonedTable.indices.indexOf(foundIndex),\n                    1,\n                )\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP INDEX \\`${indexName}\\``,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD UNIQUE INDEX \\`${indexName}\\` (\\`${column.name}\\`)`,\n                ),\n            )\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \\`${\n                    column.name\n                }\\``,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(column, true)}`,\n            ),\n        )\n\n        if (column.generatedType && column.asExpression) {\n            const currentDatabase = await this.getCurrentDatabase()\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: currentDatabase,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.removeColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.dropColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n\n        const up = this.createPrimaryKeySql(table, columnNames)\n        const down = this.dropPrimaryKeySql(table)\n\n        await this.executeQueries(up, down)\n        clonedTable.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const columnNames = columns.map((column) => column.name)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // if table have generated column, we must drop AUTO_INCREMENT before changing primary constraints.\n        const generatedColumn = clonedTable.columns.find(\n            (column) =>\n                column.isGenerated && column.generationStrategy === \"increment\",\n        )\n        if (generatedColumn) {\n            const nonGeneratedColumn = generatedColumn.clone()\n            nonGeneratedColumn.isGenerated = false\n            nonGeneratedColumn.generationStrategy = undefined\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                        generatedColumn.name\n                    }\\` ${this.buildCreateColumnSql(nonGeneratedColumn, true)}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                        nonGeneratedColumn.name\n                    }\\` ${this.buildCreateColumnSql(generatedColumn, true)}`,\n                ),\n            )\n        }\n\n        // if table already have primary columns, we must drop them.\n        const primaryColumns = clonedTable.primaryColumns\n        if (primaryColumns.length > 0) {\n            const columnNames = primaryColumns\n                .map((column) => `\\`${column.name}\\``)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} DROP PRIMARY KEY`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD PRIMARY KEY (${columnNames})`,\n                ),\n            )\n        }\n\n        // update columns in table.\n        clonedTable.columns\n            .filter((column) => columnNames.indexOf(column.name) !== -1)\n            .forEach((column) => (column.isPrimary = true))\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\\`${columnName}\\``)\n            .join(\", \")\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD PRIMARY KEY (${columnNamesString})`,\n            ),\n        )\n        downQueries.push(\n            new Query(`ALTER TABLE ${this.escapePath(table)} DROP PRIMARY KEY`),\n        )\n\n        // if we already have generated column or column is changed to generated, and we dropped AUTO_INCREMENT property before, we must bring it back\n        const newOrExistGeneratedColumn = generatedColumn\n            ? generatedColumn\n            : columns.find(\n                  (column) =>\n                      column.isGenerated &&\n                      column.generationStrategy === \"increment\",\n              )\n        if (newOrExistGeneratedColumn) {\n            const nonGeneratedColumn = newOrExistGeneratedColumn.clone()\n            nonGeneratedColumn.isGenerated = false\n            nonGeneratedColumn.generationStrategy = undefined\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                        nonGeneratedColumn.name\n                    }\\` ${this.buildCreateColumnSql(\n                        newOrExistGeneratedColumn,\n                        true,\n                    )}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} CHANGE \\`${\n                        newOrExistGeneratedColumn.name\n                    }\\` ${this.buildCreateColumnSql(nonGeneratedColumn, true)}`,\n                ),\n            )\n\n            // if column changed to generated, we must update it in table\n            const changedGeneratedColumn = clonedTable.columns.find(\n                (column) => column.name === newOrExistGeneratedColumn.name,\n            )\n            changedGeneratedColumn!.isGenerated = true\n            changedGeneratedColumn!.generationStrategy = \"increment\"\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(tableOrName: Table | string): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const up = this.dropPrimaryKeySql(table)\n        const down = this.createPrimaryKeySql(\n            table,\n            table.primaryColumns.map((column) => column.name),\n        )\n        await this.executeQueries(up, down)\n        table.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n    }\n\n    /**\n     * Creates a new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `MySql does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Creates a new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `MySql does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Drops an unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `MySql does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Drops an unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `MySql does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Creates a new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support check constraints.`)\n    }\n\n    /**\n     * Creates a new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support check constraints.`)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support check constraints.`)\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support check constraints.`)\n    }\n\n    /**\n     * Creates a new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support exclusion constraints.`)\n    }\n\n    /**\n     * Creates a new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support exclusion constraints.`)\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support exclusion constraints.`)\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(`MySql does not support exclusion constraints.`)\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new FK may be passed without name. In this case we generate FK name manually.\n        if (!foreignKey.name)\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n        const up = this.createForeignKeySql(table, foreignKey)\n        const down = this.dropForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.addForeignKey(foreignKey)\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.createForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops a foreign key.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const foreignKey = InstanceChecker.isTableForeignKey(foreignKeyOrName)\n            ? foreignKeyOrName\n            : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        const up = this.dropForeignKeySql(table, foreignKey)\n        const down = this.createForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.removeForeignKey(foreignKey)\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.dropForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.createIndexSql(table, index)\n        const down = this.dropIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.addIndex(index, true)\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.createIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops an index.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in table ${table.name}`,\n            )\n\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(table, index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index, true)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.dropIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    async clearTable(tableOrName: Table | string): Promise<void> {\n        await this.query(`TRUNCATE TABLE ${this.escapePath(tableOrName)}`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     * Be careful using this method and avoid using it in production or migrations\n     * (because it can clear all your database).\n     */\n    async clearDatabase(database?: string): Promise<void> {\n        const dbName = database ? database : this.driver.database\n        if (dbName) {\n            const isDatabaseExist = await this.hasDatabase(dbName)\n            if (!isDatabaseExist) return Promise.resolve()\n        } else {\n            throw new TypeORMError(\n                `Can not clear database. No database is specified`,\n            )\n        }\n\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            const selectViewDropsQuery = `SELECT concat('DROP VIEW IF EXISTS \\`', table_schema, '\\`.\\`', table_name, '\\`') AS \\`query\\` FROM \\`INFORMATION_SCHEMA\\`.\\`VIEWS\\` WHERE \\`TABLE_SCHEMA\\` = '${dbName}'`\n            const dropViewQueries: ObjectLiteral[] = await this.query(\n                selectViewDropsQuery,\n            )\n            await Promise.all(\n                dropViewQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            const disableForeignKeysCheckQuery = `SET FOREIGN_KEY_CHECKS = 0;`\n            const dropTablesQuery = `SELECT concat('DROP TABLE IF EXISTS \\`', table_schema, '\\`.\\`', table_name, '\\`') AS \\`query\\` FROM \\`INFORMATION_SCHEMA\\`.\\`TABLES\\` WHERE \\`TABLE_SCHEMA\\` = '${dbName}'`\n            const enableForeignKeysCheckQuery = `SET FOREIGN_KEY_CHECKS = 1;`\n\n            await this.query(disableForeignKeysCheckQuery)\n            const dropQueries: ObjectLiteral[] = await this.query(\n                dropTablesQuery,\n            )\n            await Promise.all(\n                dropQueries.map((query) => this.query(query[\"query\"])),\n            )\n            await this.query(enableForeignKeysCheckQuery)\n\n            if (!isAnotherTransactionActive) await this.commitTransaction()\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive)\n                    await this.rollbackTransaction()\n            } catch (rollbackError) {}\n            throw error\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewNames?: string[]): Promise<View[]> {\n        const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n        if (!hasTable) {\n            return []\n        }\n\n        if (!viewNames) {\n            viewNames = []\n        }\n\n        const currentDatabase = await this.getCurrentDatabase()\n        const viewsCondition = viewNames\n            .map((tableName) => {\n                let { database, tableName: name } =\n                    this.driver.parseTableName(tableName)\n\n                if (!database) {\n                    database = currentDatabase\n                }\n\n                return `(\\`t\\`.\\`schema\\` = '${database}' AND \\`t\\`.\\`name\\` = '${name}')`\n            })\n            .join(\" OR \")\n\n        const query =\n            `SELECT \\`t\\`.*, \\`v\\`.\\`check_option\\` FROM ${this.escapePath(\n                this.getTypeormMetadataTableName(),\n            )} \\`t\\` ` +\n            `INNER JOIN \\`information_schema\\`.\\`views\\` \\`v\\` ON \\`v\\`.\\`table_schema\\` = \\`t\\`.\\`schema\\` AND \\`v\\`.\\`table_name\\` = \\`t\\`.\\`name\\` WHERE \\`t\\`.\\`type\\` = '${\n                MetadataTableType.VIEW\n            }' ${viewsCondition ? `AND (${viewsCondition})` : \"\"}`\n        const dbViews = await this.query(query)\n        return dbViews.map((dbView: any) => {\n            const view = new View()\n            const db =\n                dbView[\"schema\"] === currentDatabase\n                    ? undefined\n                    : dbView[\"schema\"]\n            view.database = dbView[\"schema\"]\n            view.name = this.driver.buildTableName(\n                dbView[\"name\"],\n                undefined,\n                db,\n            )\n            view.expression = dbView[\"value\"]\n            return view\n        })\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        const currentDatabase = await this.getCurrentDatabase()\n\n        // The following SQL brought to you by:\n        //   A terrible understanding of https://dev.mysql.com/doc/refman/8.0/en/information-schema-optimization.html\n        //\n        // Short Version:\n        // INFORMATION_SCHEMA is a weird metadata virtual table and follows VERY FEW of the normal\n        // query optimization rules.  Depending on the columns you query against & the columns you're SELECTing\n        // there can be a drastically different query performance - this is because the tables map to\n        // data on the disk and some pieces of data require a scan of the data directory, the database files, etc\n\n        // With most of these, you'll want to do an `EXPLAIN` when making changes to make sure\n        // the changes you're making aren't changing the query performance profile negatively\n        // When you do the explain you'll want to look at the `Extra` field -\n        // It will look something like: \"Using where; {FILE_OPENING}; Scanned {DB_NUM} databases\"\n        // FILE_OPENING will commonly be OPEN_FRM_ONLY or OPEN_FULL_TABLE - you want to aim to NOT do\n        // an OPEN_FULL_TABLE unless necessary. DB_NUM may be a number or \"all\" - you really want to\n        // keep this to 0 or 1.  Ideally 0. \"All\" means you've scanned all databases - not good.\n        //\n        // For more info, see the above link to the MySQL docs.\n        //\n        // Something not noted in the docs is that complex `WHERE` clauses - such as `OR` expressions -\n        // will cause the query to not hit the optimizations & do full scans.  This is why\n        // a number of queries below do `UNION`s of single `WHERE` clauses.\n\n        const dbTables: {\n            TABLE_SCHEMA: string\n            TABLE_NAME: string\n            TABLE_COMMENT: string\n        }[] = []\n\n        if (!tableNames) {\n            // Since we don't have any of this data we have to do a scan\n            const tablesSql = `SELECT \\`TABLE_SCHEMA\\`, \\`TABLE_NAME\\`, \\`TABLE_COMMENT\\` FROM \\`INFORMATION_SCHEMA\\`.\\`TABLES\\``\n\n            dbTables.push(...(await this.query(tablesSql)))\n        } else {\n            // Avoid data directory scan: TABLE_SCHEMA\n            // Avoid database directory scan: TABLE_NAME\n            // We only use `TABLE_SCHEMA` and `TABLE_NAME` which is `SKIP_OPEN_TABLE`\n            const tablesSql = tableNames\n                .filter((tableName) => tableName)\n                .map((tableName) => {\n                    let { database, tableName: name } =\n                        this.driver.parseTableName(tableName)\n\n                    if (!database) {\n                        database = currentDatabase\n                    }\n\n                    return `SELECT \\`TABLE_SCHEMA\\`, \\`TABLE_NAME\\`, \\`TABLE_COMMENT\\` FROM \\`INFORMATION_SCHEMA\\`.\\`TABLES\\` WHERE \\`TABLE_SCHEMA\\` = '${database}' AND \\`TABLE_NAME\\` = '${name}'`\n                })\n                .join(\" UNION \")\n\n            dbTables.push(...(await this.query(tablesSql)))\n        }\n\n        // if tables were not found in the db, no need to proceed\n        if (!dbTables.length) return []\n\n        // Avoid data directory scan: TABLE_SCHEMA\n        // Avoid database directory scan: TABLE_NAME\n        // Full columns: CARDINALITY & INDEX_TYPE - everything else is FRM only\n        const statsSubquerySql = dbTables\n            .map(({ TABLE_SCHEMA, TABLE_NAME }) => {\n                return `\n                SELECT\n                    *\n                FROM \\`INFORMATION_SCHEMA\\`.\\`STATISTICS\\`\n                WHERE\n                    \\`TABLE_SCHEMA\\` = '${TABLE_SCHEMA}'\n                    AND\n                    \\`TABLE_NAME\\` = '${TABLE_NAME}'\n            `\n            })\n            .join(\" UNION \")\n\n        // Avoid data directory scan: TABLE_SCHEMA\n        // Avoid database directory scan: TABLE_NAME\n        // All columns will hit the full table.\n        const kcuSubquerySql = dbTables\n            .map(({ TABLE_SCHEMA, TABLE_NAME }) => {\n                return `\n                SELECT\n                    *\n                FROM \\`INFORMATION_SCHEMA\\`.\\`KEY_COLUMN_USAGE\\` \\`kcu\\`\n                WHERE\n                    \\`kcu\\`.\\`TABLE_SCHEMA\\` = '${TABLE_SCHEMA}'\n                    AND\n                    \\`kcu\\`.\\`TABLE_NAME\\` = '${TABLE_NAME}'\n            `\n            })\n            .join(\" UNION \")\n\n        // Avoid data directory scan: CONSTRAINT_SCHEMA\n        // Avoid database directory scan: TABLE_NAME\n        // All columns will hit the full table.\n        const rcSubquerySql = dbTables\n            .map(({ TABLE_SCHEMA, TABLE_NAME }) => {\n                return `\n                SELECT\n                    *\n                FROM \\`INFORMATION_SCHEMA\\`.\\`REFERENTIAL_CONSTRAINTS\\`\n                WHERE\n                    \\`CONSTRAINT_SCHEMA\\` = '${TABLE_SCHEMA}'\n                    AND\n                    \\`TABLE_NAME\\` = '${TABLE_NAME}'\n            `\n            })\n            .join(\" UNION \")\n\n        // Avoid data directory scan: TABLE_SCHEMA\n        // Avoid database directory scan: TABLE_NAME\n        // OPEN_FRM_ONLY applies to all columns\n        const columnsSql = dbTables\n            .map(({ TABLE_SCHEMA, TABLE_NAME }) => {\n                return `\n                SELECT\n                    *\n                FROM\n                    \\`INFORMATION_SCHEMA\\`.\\`COLUMNS\\`\n                WHERE\n                    \\`TABLE_SCHEMA\\` = '${TABLE_SCHEMA}'\n                    AND\n                    \\`TABLE_NAME\\` = '${TABLE_NAME}'\n                `\n            })\n            .join(\" UNION \")\n\n        // No Optimizations are available for COLLATIONS\n        const collationsSql = `\n            SELECT\n                \\`SCHEMA_NAME\\`,\n                \\`DEFAULT_CHARACTER_SET_NAME\\` as \\`CHARSET\\`,\n                \\`DEFAULT_COLLATION_NAME\\` AS \\`COLLATION\\`\n            FROM \\`INFORMATION_SCHEMA\\`.\\`SCHEMATA\\`\n            `\n\n        // Key Column Usage but only for PKs\n        const primaryKeySql = `SELECT * FROM (${kcuSubquerySql}) \\`kcu\\` WHERE \\`CONSTRAINT_NAME\\` = 'PRIMARY'`\n\n        // Combine stats & referential constraints\n        const indicesSql = `\n            SELECT\n                \\`s\\`.*\n            FROM (${statsSubquerySql}) \\`s\\`\n            LEFT JOIN (${rcSubquerySql}) \\`rc\\`\n                ON\n                    \\`s\\`.\\`INDEX_NAME\\` = \\`rc\\`.\\`CONSTRAINT_NAME\\`\n                    AND\n                    \\`s\\`.\\`TABLE_SCHEMA\\` = \\`rc\\`.\\`CONSTRAINT_SCHEMA\\`\n            WHERE\n                \\`s\\`.\\`INDEX_NAME\\` != 'PRIMARY'\n                AND\n                \\`rc\\`.\\`CONSTRAINT_NAME\\` IS NULL\n            `\n\n        // Combine Key Column Usage & Referential Constraints\n        const foreignKeysSql = `\n            SELECT\n                \\`kcu\\`.\\`TABLE_SCHEMA\\`,\n                \\`kcu\\`.\\`TABLE_NAME\\`,\n                \\`kcu\\`.\\`CONSTRAINT_NAME\\`,\n                \\`kcu\\`.\\`COLUMN_NAME\\`,\n                \\`kcu\\`.\\`REFERENCED_TABLE_SCHEMA\\`,\n                \\`kcu\\`.\\`REFERENCED_TABLE_NAME\\`,\n                \\`kcu\\`.\\`REFERENCED_COLUMN_NAME\\`,\n                \\`rc\\`.\\`DELETE_RULE\\` \\`ON_DELETE\\`,\n                \\`rc\\`.\\`UPDATE_RULE\\` \\`ON_UPDATE\\`\n            FROM (${kcuSubquerySql}) \\`kcu\\`\n            INNER JOIN (${rcSubquerySql}) \\`rc\\`\n                ON\n                    \\`rc\\`.\\`CONSTRAINT_SCHEMA\\` = \\`kcu\\`.\\`CONSTRAINT_SCHEMA\\`\n                    AND\n                    \\`rc\\`.\\`TABLE_NAME\\` = \\`kcu\\`.\\`TABLE_NAME\\`\n                    AND\n                    \\`rc\\`.\\`CONSTRAINT_NAME\\` = \\`kcu\\`.\\`CONSTRAINT_NAME\\`\n            `\n\n        const [\n            dbColumns,\n            dbPrimaryKeys,\n            dbCollations,\n            dbIndices,\n            dbForeignKeys,\n        ]: ObjectLiteral[][] = await Promise.all([\n            this.query(columnsSql),\n            this.query(primaryKeySql),\n            this.query(collationsSql),\n            this.query(indicesSql),\n            this.query(foreignKeysSql),\n        ])\n\n        const isMariaDb = this.driver.options.type === \"mariadb\"\n        const dbVersion = this.driver.version\n\n        // create tables for loaded tables\n        return Promise.all(\n            dbTables.map(async (dbTable) => {\n                const table = new Table()\n\n                const dbCollation = dbCollations.find(\n                    (coll) => coll[\"SCHEMA_NAME\"] === dbTable[\"TABLE_SCHEMA\"],\n                )!\n                const defaultCollation = dbCollation[\"COLLATION\"]\n                const defaultCharset = dbCollation[\"CHARSET\"]\n\n                // We do not need to join database name, when database is by default.\n                const db =\n                    dbTable[\"TABLE_SCHEMA\"] === currentDatabase\n                        ? undefined\n                        : dbTable[\"TABLE_SCHEMA\"]\n                table.database = dbTable[\"TABLE_SCHEMA\"]\n                table.name = this.driver.buildTableName(\n                    dbTable[\"TABLE_NAME\"],\n                    undefined,\n                    db,\n                )\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns\n                        .filter(\n                            (dbColumn) =>\n                                dbColumn[\"TABLE_NAME\"] ===\n                                    dbTable[\"TABLE_NAME\"] &&\n                                dbColumn[\"TABLE_SCHEMA\"] ===\n                                    dbTable[\"TABLE_SCHEMA\"],\n                        )\n                        .map(async (dbColumn) => {\n                            const columnUniqueIndices = dbIndices.filter(\n                                (dbIndex) => {\n                                    return (\n                                        dbIndex[\"TABLE_NAME\"] ===\n                                            dbTable[\"TABLE_NAME\"] &&\n                                        dbIndex[\"TABLE_SCHEMA\"] ===\n                                            dbTable[\"TABLE_SCHEMA\"] &&\n                                        dbIndex[\"COLUMN_NAME\"] ===\n                                            dbColumn[\"COLUMN_NAME\"] &&\n                                        parseInt(dbIndex[\"NON_UNIQUE\"], 10) ===\n                                            0\n                                    )\n                                },\n                            )\n\n                            const tableMetadata =\n                                this.connection.entityMetadatas.find(\n                                    (metadata) =>\n                                        this.getTablePath(table) ===\n                                        this.getTablePath(metadata),\n                                )\n                            const hasIgnoredIndex =\n                                columnUniqueIndices.length > 0 &&\n                                tableMetadata &&\n                                tableMetadata.indices.some((index) => {\n                                    return columnUniqueIndices.some(\n                                        (uniqueIndex) => {\n                                            return (\n                                                index.name ===\n                                                    uniqueIndex[\"INDEX_NAME\"] &&\n                                                index.synchronize === false\n                                            )\n                                        },\n                                    )\n                                })\n\n                            const isConstraintComposite =\n                                columnUniqueIndices.every((uniqueIndex) => {\n                                    return dbIndices.some(\n                                        (dbIndex) =>\n                                            dbIndex[\"INDEX_NAME\"] ===\n                                                uniqueIndex[\"INDEX_NAME\"] &&\n                                            dbIndex[\"COLUMN_NAME\"] !==\n                                                dbColumn[\"COLUMN_NAME\"],\n                                    )\n                                })\n\n                            const tableColumn = new TableColumn()\n                            tableColumn.name = dbColumn[\"COLUMN_NAME\"]\n                            tableColumn.type =\n                                dbColumn[\"DATA_TYPE\"].toLowerCase()\n\n                            // since mysql 8.0, \"geometrycollection\" returned as \"geomcollection\"\n                            // typeorm still use \"geometrycollection\"\n                            if (tableColumn.type === \"geomcollection\") {\n                                tableColumn.type = \"geometrycollection\"\n                            }\n\n                            tableColumn.zerofill =\n                                dbColumn[\"COLUMN_TYPE\"].indexOf(\"zerofill\") !==\n                                -1\n                            tableColumn.unsigned = tableColumn.zerofill\n                                ? true\n                                : dbColumn[\"COLUMN_TYPE\"].indexOf(\n                                      \"unsigned\",\n                                  ) !== -1\n                            if (\n                                this.driver.withWidthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1\n                            ) {\n                                const width = dbColumn[\"COLUMN_TYPE\"].substring(\n                                    dbColumn[\"COLUMN_TYPE\"].indexOf(\"(\") + 1,\n                                    dbColumn[\"COLUMN_TYPE\"].indexOf(\")\"),\n                                )\n                                tableColumn.width =\n                                    width &&\n                                    !this.isDefaultColumnWidth(\n                                        table,\n                                        tableColumn,\n                                        parseInt(width),\n                                    )\n                                        ? parseInt(width)\n                                        : undefined\n                            }\n\n                            if (\n                                dbColumn[\"COLUMN_DEFAULT\"] === null ||\n                                dbColumn[\"COLUMN_DEFAULT\"] === undefined ||\n                                (isMariaDb &&\n                                    dbColumn[\"COLUMN_DEFAULT\"] === \"NULL\")\n                            ) {\n                                tableColumn.default = undefined\n                            } else if (\n                                /^CURRENT_TIMESTAMP(\\([0-9]*\\))?$/i.test(\n                                    dbColumn[\"COLUMN_DEFAULT\"],\n                                )\n                            ) {\n                                // New versions of MariaDB return expressions in lowercase.  We need to set it in\n                                // uppercase so the comparison in MysqlDriver#compareDefaultValues does not fail.\n                                tableColumn.default =\n                                    dbColumn[\"COLUMN_DEFAULT\"].toUpperCase()\n                            } else if (\n                                isMariaDb &&\n                                VersionUtils.isGreaterOrEqual(\n                                    dbVersion,\n                                    \"10.2.7\",\n                                )\n                            ) {\n                                // MariaDB started adding quotes to literals in COLUMN_DEFAULT since version 10.2.7\n                                // See https://mariadb.com/kb/en/library/information-schema-columns-table/\n                                tableColumn.default = dbColumn[\"COLUMN_DEFAULT\"]\n                            } else {\n                                tableColumn.default = `'${dbColumn[\"COLUMN_DEFAULT\"]}'`\n                            }\n\n                            if (dbColumn[\"EXTRA\"].indexOf(\"on update\") !== -1) {\n                                // New versions of MariaDB return expressions in lowercase.  We need to set it in\n                                // uppercase so the comparison in MysqlDriver#compareExtraValues does not fail.\n                                tableColumn.onUpdate = dbColumn[\"EXTRA\"]\n                                    .substring(\n                                        dbColumn[\"EXTRA\"].indexOf(\"on update\") +\n                                            10,\n                                    )\n                                    .toUpperCase()\n                            }\n\n                            if (dbColumn[\"GENERATION_EXPRESSION\"]) {\n                                tableColumn.generatedType =\n                                    dbColumn[\"EXTRA\"].indexOf(\"VIRTUAL\") !== -1\n                                        ? \"VIRTUAL\"\n                                        : \"STORED\"\n\n                                // We cannot relay on information_schema.columns.generation_expression, because it is formatted different.\n                                const asExpressionQuery =\n                                    this.selectTypeormMetadataSql({\n                                        schema: dbTable[\"TABLE_SCHEMA\"],\n                                        table: dbTable[\"TABLE_NAME\"],\n                                        type: MetadataTableType.GENERATED_COLUMN,\n                                        name: tableColumn.name,\n                                    })\n\n                                const results = await this.query(\n                                    asExpressionQuery.query,\n                                    asExpressionQuery.parameters,\n                                )\n                                if (results[0] && results[0].value) {\n                                    tableColumn.asExpression = results[0].value\n                                } else {\n                                    tableColumn.asExpression = \"\"\n                                }\n                            }\n\n                            tableColumn.isUnique =\n                                columnUniqueIndices.length > 0 &&\n                                !hasIgnoredIndex &&\n                                !isConstraintComposite\n\n                            if (isMariaDb && tableColumn.generatedType) {\n                                // do nothing - MariaDB does not support NULL/NOT NULL expressions for generated columns\n                            } else {\n                                tableColumn.isNullable =\n                                    dbColumn[\"IS_NULLABLE\"] === \"YES\"\n                            }\n\n                            tableColumn.isPrimary = dbPrimaryKeys.some(\n                                (dbPrimaryKey) => {\n                                    return (\n                                        dbPrimaryKey[\"TABLE_NAME\"] ===\n                                            dbColumn[\"TABLE_NAME\"] &&\n                                        dbPrimaryKey[\"TABLE_SCHEMA\"] ===\n                                            dbColumn[\"TABLE_SCHEMA\"] &&\n                                        dbPrimaryKey[\"COLUMN_NAME\"] ===\n                                            dbColumn[\"COLUMN_NAME\"]\n                                    )\n                                },\n                            )\n                            tableColumn.isGenerated =\n                                dbColumn[\"EXTRA\"].indexOf(\"auto_increment\") !==\n                                -1\n                            if (tableColumn.isGenerated)\n                                tableColumn.generationStrategy = \"increment\"\n\n                            tableColumn.comment =\n                                typeof dbColumn[\"COLUMN_COMMENT\"] ===\n                                    \"string\" &&\n                                dbColumn[\"COLUMN_COMMENT\"].length === 0\n                                    ? undefined\n                                    : dbColumn[\"COLUMN_COMMENT\"]\n                            if (dbColumn[\"CHARACTER_SET_NAME\"])\n                                tableColumn.charset =\n                                    dbColumn[\"CHARACTER_SET_NAME\"] ===\n                                    defaultCharset\n                                        ? undefined\n                                        : dbColumn[\"CHARACTER_SET_NAME\"]\n                            if (dbColumn[\"COLLATION_NAME\"])\n                                tableColumn.collation =\n                                    dbColumn[\"COLLATION_NAME\"] ===\n                                    defaultCollation\n                                        ? undefined\n                                        : dbColumn[\"COLLATION_NAME\"]\n\n                            // check only columns that have length property\n                            if (\n                                this.driver.withLengthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1 &&\n                                dbColumn[\"CHARACTER_MAXIMUM_LENGTH\"]\n                            ) {\n                                const length =\n                                    dbColumn[\n                                        \"CHARACTER_MAXIMUM_LENGTH\"\n                                    ].toString()\n                                tableColumn.length =\n                                    !this.isDefaultColumnLength(\n                                        table,\n                                        tableColumn,\n                                        length,\n                                    )\n                                        ? length\n                                        : \"\"\n                            }\n\n                            if (\n                                tableColumn.type === \"decimal\" ||\n                                tableColumn.type === \"double\" ||\n                                tableColumn.type === \"float\"\n                            ) {\n                                if (\n                                    dbColumn[\"NUMERIC_PRECISION\"] !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"NUMERIC_PRECISION\"],\n                                    )\n                                )\n                                    tableColumn.precision = parseInt(\n                                        dbColumn[\"NUMERIC_PRECISION\"],\n                                    )\n                                if (\n                                    dbColumn[\"NUMERIC_SCALE\"] !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"NUMERIC_SCALE\"],\n                                    )\n                                )\n                                    tableColumn.scale = parseInt(\n                                        dbColumn[\"NUMERIC_SCALE\"],\n                                    )\n                            }\n\n                            if (\n                                tableColumn.type === \"enum\" ||\n                                tableColumn.type === \"simple-enum\" ||\n                                tableColumn.type === \"set\"\n                            ) {\n                                const colType = dbColumn[\"COLUMN_TYPE\"]\n                                const items = colType\n                                    .substring(\n                                        colType.indexOf(\"(\") + 1,\n                                        colType.lastIndexOf(\")\"),\n                                    )\n                                    .split(\",\")\n                                tableColumn.enum = (items as string[]).map(\n                                    (item) => {\n                                        return item.substring(\n                                            1,\n                                            item.length - 1,\n                                        )\n                                    },\n                                )\n                                tableColumn.length = \"\"\n                            }\n\n                            if (\n                                (tableColumn.type === \"datetime\" ||\n                                    tableColumn.type === \"time\" ||\n                                    tableColumn.type === \"timestamp\") &&\n                                dbColumn[\"DATETIME_PRECISION\"] !== null &&\n                                dbColumn[\"DATETIME_PRECISION\"] !== undefined &&\n                                !this.isDefaultColumnPrecision(\n                                    table,\n                                    tableColumn,\n                                    parseInt(dbColumn[\"DATETIME_PRECISION\"]),\n                                )\n                            ) {\n                                tableColumn.precision = parseInt(\n                                    dbColumn[\"DATETIME_PRECISION\"],\n                                )\n                            }\n\n                            return tableColumn\n                        }),\n                )\n\n                // find foreign key constraints of table, group them by constraint name and build TableForeignKey.\n                const tableForeignKeyConstraints = OrmUtils.uniq(\n                    dbForeignKeys.filter((dbForeignKey) => {\n                        return (\n                            dbForeignKey[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbForeignKey[\"TABLE_SCHEMA\"] ===\n                                dbTable[\"TABLE_SCHEMA\"]\n                        )\n                    }),\n                    (dbForeignKey) => dbForeignKey[\"CONSTRAINT_NAME\"],\n                )\n\n                table.foreignKeys = tableForeignKeyConstraints.map(\n                    (dbForeignKey) => {\n                        const foreignKeys = dbForeignKeys.filter(\n                            (dbFk) =>\n                                dbFk[\"CONSTRAINT_NAME\"] ===\n                                dbForeignKey[\"CONSTRAINT_NAME\"],\n                        )\n\n                        // if referenced table located in currently used db, we don't need to concat db name to table name.\n                        const database =\n                            dbForeignKey[\"REFERENCED_TABLE_SCHEMA\"] ===\n                            currentDatabase\n                                ? undefined\n                                : dbForeignKey[\"REFERENCED_TABLE_SCHEMA\"]\n                        const referencedTableName = this.driver.buildTableName(\n                            dbForeignKey[\"REFERENCED_TABLE_NAME\"],\n                            undefined,\n                            database,\n                        )\n\n                        return new TableForeignKey({\n                            name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                            columnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"COLUMN_NAME\"],\n                            ),\n                            referencedDatabase:\n                                dbForeignKey[\"REFERENCED_TABLE_SCHEMA\"],\n                            referencedTableName: referencedTableName,\n                            referencedColumnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                            ),\n                            onDelete: dbForeignKey[\"ON_DELETE\"],\n                            onUpdate: dbForeignKey[\"ON_UPDATE\"],\n                        })\n                    },\n                )\n\n                // find index constraints of table, group them by constraint name and build TableIndex.\n                const tableIndexConstraints = OrmUtils.uniq(\n                    dbIndices.filter(\n                        (dbIndex) =>\n                            dbIndex[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"] &&\n                            dbIndex[\"TABLE_SCHEMA\"] === dbTable[\"TABLE_SCHEMA\"],\n                    ),\n                    (dbIndex) => dbIndex[\"INDEX_NAME\"],\n                )\n\n                table.indices = tableIndexConstraints.map((constraint) => {\n                    const indices = dbIndices.filter((index) => {\n                        return (\n                            index[\"TABLE_SCHEMA\"] ===\n                                constraint[\"TABLE_SCHEMA\"] &&\n                            index[\"TABLE_NAME\"] === constraint[\"TABLE_NAME\"] &&\n                            index[\"INDEX_NAME\"] === constraint[\"INDEX_NAME\"]\n                        )\n                    })\n\n                    const nonUnique = parseInt(constraint[\"NON_UNIQUE\"], 10)\n\n                    return new TableIndex(<TableIndexOptions>{\n                        table: table,\n                        name: constraint[\"INDEX_NAME\"],\n                        columnNames: indices.map((i) => i[\"COLUMN_NAME\"]),\n                        isUnique: nonUnique === 0,\n                        isSpatial: constraint[\"INDEX_TYPE\"] === \"SPATIAL\",\n                        isFulltext: constraint[\"INDEX_TYPE\"] === \"FULLTEXT\",\n                    })\n                })\n\n                table.comment = dbTable[\"TABLE_COMMENT\"]\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds create table sql\n     */\n    protected createTableSql(table: Table, createForeignKeys?: boolean): Query {\n        const columnDefinitions = table.columns\n            .map((column) => this.buildCreateColumnSql(column, true))\n            .join(\", \")\n        let sql = `CREATE TABLE ${this.escapePath(table)} (${columnDefinitions}`\n\n        // we create unique indexes instead of unique constraints, because MySql does not have unique constraints.\n        // if we mark column as Unique, it means that we create UNIQUE INDEX.\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueIndexExist = table.indices.some((index) => {\n                    return (\n                        index.columnNames.length === 1 &&\n                        !!index.isUnique &&\n                        index.columnNames.indexOf(column.name) !== -1\n                    )\n                })\n                const isUniqueConstraintExist = table.uniques.some((unique) => {\n                    return (\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames.indexOf(column.name) !== -1\n                    )\n                })\n                if (!isUniqueIndexExist && !isUniqueConstraintExist)\n                    table.indices.push(\n                        new TableIndex({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                            isUnique: true,\n                        }),\n                    )\n            })\n\n        // as MySql does not have unique constraints, we must create table indices from table uniques and mark them as unique.\n        if (table.uniques.length > 0) {\n            table.uniques.forEach((unique) => {\n                const uniqueExist = table.indices.some(\n                    (index) => index.name === unique.name,\n                )\n                if (!uniqueExist) {\n                    table.indices.push(\n                        new TableIndex({\n                            name: unique.name,\n                            columnNames: unique.columnNames,\n                            isUnique: true,\n                        }),\n                    )\n                }\n            })\n        }\n\n        if (table.indices.length > 0) {\n            const indicesSql = table.indices\n                .map((index) => {\n                    const columnNames = index.columnNames\n                        .map((columnName) => `\\`${columnName}\\``)\n                        .join(\", \")\n                    if (!index.name)\n                        index.name = this.connection.namingStrategy.indexName(\n                            table,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    let indexType = \"\"\n                    if (index.isUnique) indexType += \"UNIQUE \"\n                    if (index.isSpatial) indexType += \"SPATIAL \"\n                    if (index.isFulltext) indexType += \"FULLTEXT \"\n                    const indexParser =\n                        index.isFulltext && index.parser\n                            ? ` WITH PARSER ${index.parser}`\n                            : \"\"\n\n                    return `${indexType}INDEX \\`${index.name}\\` (${columnNames})${indexParser}`\n                })\n                .join(\", \")\n\n            sql += `, ${indicesSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .map((fk) => {\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\\`${columnName}\\``)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            table,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\\`${columnName}\\``)\n                        .join(\", \")\n\n                    let constraint = `CONSTRAINT \\`${\n                        fk.name\n                    }\\` FOREIGN KEY (${columnNames}) REFERENCES ${this.escapePath(\n                        this.getTablePath(fk),\n                    )} (${referencedColumnNames})`\n                    if (fk.onDelete) constraint += ` ON DELETE ${fk.onDelete}`\n                    if (fk.onUpdate) constraint += ` ON UPDATE ${fk.onUpdate}`\n\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        if (table.primaryColumns.length > 0) {\n            const columnNames = table.primaryColumns\n                .map((column) => `\\`${column.name}\\``)\n                .join(\", \")\n            sql += `, PRIMARY KEY (${columnNames})`\n        }\n\n        sql += `) ENGINE=${table.engine || \"InnoDB\"}`\n\n        if (table.comment) {\n            sql += ` COMMENT=\"${table.comment}\"`\n        }\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop table sql\n     */\n    protected dropTableSql(tableOrName: Table | string): Query {\n        return new Query(`DROP TABLE ${this.escapePath(tableOrName)}`)\n    }\n\n    protected createViewSql(view: View): Query {\n        if (typeof view.expression === \"string\") {\n            return new Query(\n                `CREATE VIEW ${this.escapePath(view)} AS ${view.expression}`,\n            )\n        } else {\n            return new Query(\n                `CREATE VIEW ${this.escapePath(view)} AS ${view\n                    .expression(this.connection)\n                    .getQuery()}`,\n            )\n        }\n    }\n\n    protected async insertViewDefinitionSql(view: View): Promise<Query> {\n        const currentDatabase = await this.getCurrentDatabase()\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        return this.insertTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            schema: currentDatabase,\n            name: view.name,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(viewOrPath: View | string): Query {\n        return new Query(`DROP VIEW ${this.escapePath(viewOrPath)}`)\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected async deleteViewDefinitionSql(\n        viewOrPath: View | string,\n    ): Promise<Query> {\n        const currentDatabase = await this.getCurrentDatabase()\n        const viewName = InstanceChecker.isView(viewOrPath)\n            ? viewOrPath.name\n            : viewOrPath\n        return this.deleteTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            schema: currentDatabase,\n            name: viewName,\n        })\n    }\n\n    /**\n     * Builds create index sql.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\\`${columnName}\\``)\n            .join(\", \")\n        let indexType = \"\"\n        if (index.isUnique) indexType += \"UNIQUE \"\n        if (index.isSpatial) indexType += \"SPATIAL \"\n        if (index.isFulltext) indexType += \"FULLTEXT \"\n        const indexParser =\n            index.isFulltext && index.parser\n                ? ` WITH PARSER ${index.parser}`\n                : \"\"\n\n        return new Query(\n            `CREATE ${indexType}INDEX \\`${index.name}\\` ON ${this.escapePath(\n                table,\n            )} (${columns})${indexParser}`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(\n        table: Table,\n        indexOrName: TableIndex | string,\n    ): Query {\n        const indexName = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName.name\n            : indexOrName\n        return new Query(\n            `DROP INDEX \\`${indexName}\\` ON ${this.escapePath(table)}`,\n        )\n    }\n\n    /**\n     * Builds create primary key sql.\n     */\n    protected createPrimaryKeySql(table: Table, columnNames: string[]): Query {\n        const columnNamesString = columnNames\n            .map((columnName) => `\\`${columnName}\\``)\n            .join(\", \")\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} ADD PRIMARY KEY (${columnNamesString})`,\n        )\n    }\n\n    /**\n     * Builds drop primary key sql.\n     */\n    protected dropPrimaryKeySql(table: Table): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} DROP PRIMARY KEY`,\n        )\n    }\n\n    /**\n     * Builds create foreign key sql.\n     */\n    protected createForeignKeySql(\n        table: Table,\n        foreignKey: TableForeignKey,\n    ): Query {\n        const columnNames = foreignKey.columnNames\n            .map((column) => `\\`${column}\\``)\n            .join(\", \")\n        const referencedColumnNames = foreignKey.referencedColumnNames\n            .map((column) => `\\`${column}\\``)\n            .join(\",\")\n        let sql =\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \\`${\n                foreignKey.name\n            }\\` FOREIGN KEY (${columnNames}) ` +\n            `REFERENCES ${this.escapePath(\n                this.getTablePath(foreignKey),\n            )}(${referencedColumnNames})`\n        if (foreignKey.onDelete) sql += ` ON DELETE ${foreignKey.onDelete}`\n        if (foreignKey.onUpdate) sql += ` ON UPDATE ${foreignKey.onUpdate}`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop foreign key sql.\n     */\n    protected dropForeignKeySql(\n        table: Table,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Query {\n        const foreignKeyName = InstanceChecker.isTableForeignKey(\n            foreignKeyOrName,\n        )\n            ? foreignKeyOrName.name\n            : foreignKeyOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP FOREIGN KEY \\`${foreignKeyName}\\``,\n        )\n    }\n\n    /**\n     * Escapes a given comment so it's safe to include in a query.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment || comment.length === 0) {\n            return `''`\n        }\n\n        comment = comment\n            .replace(/\\\\/g, \"\\\\\\\\\") // MySQL allows escaping characters via backslashes\n            .replace(/'/g, \"''\")\n            .replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return `'${comment}'`\n    }\n\n    /**\n     * Escapes given table or view path.\n     */\n    protected escapePath(target: Table | View | string): string {\n        const { database, tableName } = this.driver.parseTableName(target)\n\n        if (database && database !== this.driver.database) {\n            return `\\`${database}\\`.\\`${tableName}\\``\n        }\n\n        return `\\`${tableName}\\``\n    }\n\n    /**\n     * Builds a part of query to create/change a column.\n     */\n    protected buildCreateColumnSql(\n        column: TableColumn,\n        skipPrimary: boolean,\n        skipName: boolean = false,\n    ) {\n        let c = \"\"\n        if (skipName) {\n            c = this.connection.driver.createFullType(column)\n        } else {\n            c = `\\`${column.name}\\` ${this.connection.driver.createFullType(\n                column,\n            )}`\n        }\n\n        if (column.charset) c += ` CHARACTER SET \"${column.charset}\"`\n        if (column.collation) c += ` COLLATE \"${column.collation}\"`\n\n        if (column.asExpression)\n            c += ` AS (${column.asExpression}) ${\n                column.generatedType ? column.generatedType : \"VIRTUAL\"\n            }`\n\n        // if you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to that column.\n        if (column.zerofill) {\n            c += \" ZEROFILL\"\n        } else if (column.unsigned) {\n            c += \" UNSIGNED\"\n        }\n        if (column.enum)\n            c += ` (${column.enum\n                .map((value) => \"'\" + value.replace(/'/g, \"''\") + \"'\")\n                .join(\", \")})`\n\n        const isMariaDb = this.driver.options.type === \"mariadb\"\n        if (\n            isMariaDb &&\n            column.asExpression &&\n            [\"VIRTUAL\", \"STORED\"].includes(column.generatedType || \"VIRTUAL\")\n        ) {\n            // do nothing - MariaDB does not support NULL/NOT NULL expressions for VIRTUAL columns and STORED columns\n        } else {\n            if (!column.isNullable) c += \" NOT NULL\"\n            if (column.isNullable) c += \" NULL\"\n        }\n\n        if (column.isPrimary && !skipPrimary) c += \" PRIMARY KEY\"\n        if (column.isGenerated && column.generationStrategy === \"increment\")\n            // don't use skipPrimary here since updates can update already exist primary without auto inc.\n            c += \" AUTO_INCREMENT\"\n        if (column.comment && column.comment.length > 0)\n            c += ` COMMENT ${this.escapeComment(column.comment)}`\n        if (column.default !== undefined && column.default !== null)\n            c += ` DEFAULT ${column.default}`\n        if (column.onUpdate) c += ` ON UPDATE ${column.onUpdate}`\n\n        return c\n    }\n\n    async getVersion(): Promise<string> {\n        const result: [{ version: string }] = await this.query(\n            `SELECT VERSION() AS \\`version\\``,\n        )\n\n        // MariaDB: https://mariadb.com/kb/en/version/\n        // - \"10.2.27-MariaDB-10.2.27+maria~jessie-log\"\n        // MySQL: https://dev.mysql.com/doc/refman/8.4/en/information-functions.html#function_version\n        // - \"8.4.3\"\n        // - \"8.4.4-standard\"\n        const versionString = result[0].version\n\n        return versionString.replace(/^([\\d.]+).*$/, \"$1\")\n    }\n\n    /**\n     * Checks if column display width is by default.\n     */\n    protected isDefaultColumnWidth(\n        table: Table,\n        column: TableColumn,\n        width: number,\n    ): boolean {\n        // if table have metadata, we check if length is specified in column metadata\n        if (this.connection.hasMetadata(table.name)) {\n            const metadata = this.connection.getMetadata(table.name)\n            const columnMetadata = metadata.findColumnWithDatabaseName(\n                column.name,\n            )\n            if (columnMetadata && columnMetadata.width) return false\n        }\n\n        const defaultWidthForType =\n            this.connection.driver.dataTypeDefaults &&\n            this.connection.driver.dataTypeDefaults[column.type] &&\n            this.connection.driver.dataTypeDefaults[column.type].width\n\n        if (defaultWidthForType) {\n            // In MariaDB & MySQL 5.7, the default widths of certain numeric types are 1 less than\n            // the usual defaults when the column is unsigned.\n            const typesWithReducedUnsignedDefault = [\n                \"int\",\n                \"tinyint\",\n                \"smallint\",\n                \"mediumint\",\n            ]\n            const needsAdjustment =\n                typesWithReducedUnsignedDefault.indexOf(column.type) !== -1\n            if (column.unsigned && needsAdjustment) {\n                return defaultWidthForType - 1 === width\n            } else {\n                return defaultWidthForType === width\n            }\n        }\n\n        return false\n    }\n}\n"], "sourceRoot": "../.."}