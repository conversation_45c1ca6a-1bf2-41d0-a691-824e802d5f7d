{"version": 3, "sources": ["../../src/decorator/ForeignKey.ts"], "names": [], "mappings": ";;AAgDA,gCAuDC;AAtGD,wCAAmD;AAEnD,qDAAiD;AAwCjD;;;;GAIG;AACH,SAAgB,UAAU,CAKtB,oBAA8D,EAC9D,iCAIuB,EACvB,8BAEuB,EACvB,YAAgC;IAEhC,MAAM,WAAW,GACb,OAAO,iCAAiC,KAAK,QAAQ;QACrD,OAAO,iCAAiC,KAAK,UAAU;QACnD,CAAC,CAAC,iCAAiC;QACnC,CAAC,CAAC,SAAS,CAAA;IAEnB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,iCAAiC,CAAC;QAChE,CAAC,CAAC,iCAAiC;QACnC,CAAC,CAAC,SAAS,CAAA;IAEf,MAAM,qBAAqB,GAAG,KAAK,CAAC,OAAO,CAAC,8BAA8B,CAAC;QACvE,CAAC,CAAC,8BAA8B;QAChC,CAAC,CAAC,SAAS,CAAA;IAEf,MAAM,OAAO,GACT,yBAAW,CAAC,QAAQ,CAAC,iCAAiC,CAAC;QACvD,CAAC,KAAK,CAAC,OAAO,CAAC,iCAAiC,CAAC;QAC7C,CAAC,CAAC,iCAAiC;QACnC,CAAC,CAAC,yBAAW,CAAC,QAAQ,CAAC,8BAA8B,CAAC;YACpD,CAAC,KAAK,CAAC,OAAO,CAAC,8BAA8B,CAAC;YAChD,CAAC,CAAC,8BAA8B;YAChC,CAAC,CAAC,YAAY,CAAA;IAEtB,OAAO,UACH,WAA8B,EAC9B,YAA8B;QAE9B,IAAA,gCAAsB,GAAE,CAAC,WAAW,CAAC,IAAI,CAAC;YACtC,MAAM,EAAE,YAAY;gBAChB,CAAC,CAAC,WAAW,CAAC,WAAW;gBACzB,CAAC,CAAE,WAAwB;YAC/B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,oBAAoB;YAC1B,WAAW;YACX,WAAW;YACX,qBAAqB;YACrB,GAAI,OAA6B;SACV,CAAC,CAAA;IAChC,CAAC,CAAA;AACL,CAAC", "file": "ForeignKey.js", "sourcesContent": ["import { ObjectType } from \"../common/ObjectType\"\nimport { getMetadataArgsStorage } from \"../globals\"\nimport { ForeignKeyMetadataArgs } from \"../metadata-args/ForeignKeyMetadataArgs\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { ForeignKeyOptions } from \"./options/ForeignKeyOptions\"\n\n/**\n * Creates a database foreign key. Can be used on entity property or on entity.\n * Can create foreign key with composite columns when used on entity.\n * Warning! Don't use this with relations; relation decorators create foreign keys automatically.\n */\nexport function ForeignKey<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    options?: ForeignKeyOptions,\n): PropertyDecorator\n\n/**\n * Creates a database foreign key. Can be used on entity property or on entity.\n * Can create foreign key with composite columns when used on entity.\n * Warning! Don't use this with relations; relation decorators create foreign keys automatically.\n */\nexport function ForeignKey<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    inverseSide: string | ((object: T) => any),\n    options?: ForeignKeyOptions,\n): PropertyDecorator\n\n/**\n * Creates a database foreign key. Can be used on entity property or on entity.\n * Can create foreign key with composite columns when used on entity.\n * Warning! Don't use this with relations; relation decorators create foreign keys automatically.\n */\nexport function ForeignKey<\n    T,\n    C extends (readonly [] | readonly string[]) &\n        (number extends C[\"length\"] ? readonly [] : unknown),\n>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    columnNames: C,\n    referencedColumnNames: { [K in keyof C]: string },\n    options?: ForeignKeyOptions,\n): ClassDecorator\n\n/**\n * Creates a database foreign key. Can be used on entity property or on entity.\n * Can create foreign key with composite columns when used on entity.\n * Warning! Don't use this with relations; relation decorators create foreign keys automatically.\n */\nexport function ForeignKey<\n    T,\n    C extends (readonly [] | readonly string[]) &\n        (number extends C[\"length\"] ? readonly [] : unknown),\n>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    inverseSideOrColumnNamesOrOptions?:\n        | string\n        | ((object: T) => any)\n        | C\n        | ForeignKeyOptions,\n    referencedColumnNamesOrOptions?:\n        | { [K in keyof C]: string }\n        | ForeignKeyOptions,\n    maybeOptions?: ForeignKeyOptions,\n): ClassDecorator & PropertyDecorator {\n    const inverseSide =\n        typeof inverseSideOrColumnNamesOrOptions === \"string\" ||\n        typeof inverseSideOrColumnNamesOrOptions === \"function\"\n            ? inverseSideOrColumnNamesOrOptions\n            : undefined\n\n    const columnNames = Array.isArray(inverseSideOrColumnNamesOrOptions)\n        ? inverseSideOrColumnNamesOrOptions\n        : undefined\n\n    const referencedColumnNames = Array.isArray(referencedColumnNamesOrOptions)\n        ? referencedColumnNamesOrOptions\n        : undefined\n\n    const options =\n        ObjectUtils.isObject(inverseSideOrColumnNamesOrOptions) &&\n        !Array.isArray(inverseSideOrColumnNamesOrOptions)\n            ? inverseSideOrColumnNamesOrOptions\n            : ObjectUtils.isObject(referencedColumnNamesOrOptions) &&\n              !Array.isArray(referencedColumnNamesOrOptions)\n            ? referencedColumnNamesOrOptions\n            : maybeOptions\n\n    return function (\n        clsOrObject: Function | Object,\n        propertyName?: string | symbol,\n    ) {\n        getMetadataArgsStorage().foreignKeys.push({\n            target: propertyName\n                ? clsOrObject.constructor\n                : (clsOrObject as Function),\n            propertyName: propertyName,\n            type: typeFunctionOrTarget,\n            inverseSide,\n            columnNames,\n            referencedColumnNames,\n            ...(options as ForeignKeyOptions),\n        } as ForeignKeyMetadataArgs)\n    }\n}\n"], "sourceRoot": ".."}