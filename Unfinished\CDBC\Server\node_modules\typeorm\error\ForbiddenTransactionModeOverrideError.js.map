{"version": 3, "sources": ["../../src/error/ForbiddenTransactionModeOverrideError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,qCAAsC,SAAQ,2BAAY;IACnE,YAAY,mCAAgD;QACxD,MAAM,cAAc,GAAG,mCAAmC,CAAC,GAAG,CAC1D,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,GAAG,CACvC,CAAA;QAED,KAAK,CACD,cAAc,cAAc,CAAC,IAAI,CAC7B,IAAI,CACP,0EAA0E,CAC9E,CAAA;IACL,CAAC;CACJ;AAZD,sFAYC", "file": "ForbiddenTransactionModeOverrideError.js", "sourcesContent": ["import { Migration } from \"../migration/Migration\"\nimport { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when the per-migration transaction mode is overriden but the global transaction mode is set to \"all\".\n */\nexport class ForbiddenTransactionModeOverrideError extends TypeORMError {\n    constructor(migrationsOverridingTransactionMode: Migration[]) {\n        const migrationNames = migrationsOverridingTransactionMode.map(\n            (migration) => `\"${migration.name}\"`,\n        )\n\n        super(\n            `Migrations ${migrationNames.join(\n                \", \",\n            )} override the transaction mode, but the global transaction mode is \"all\"`,\n        )\n    }\n}\n"], "sourceRoot": ".."}