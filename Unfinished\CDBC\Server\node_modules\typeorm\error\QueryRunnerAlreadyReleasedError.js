"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryRunnerAlreadyReleasedError = void 0;
const TypeORMError_1 = require("./TypeORMError");
class QueryRunnerAlreadyReleasedError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`Query runner already released. Cannot run queries anymore.`);
    }
}
exports.QueryRunnerAlreadyReleasedError = QueryRunnerAlreadyReleasedError;

//# sourceMappingURL=QueryRunnerAlreadyReleasedError.js.map
