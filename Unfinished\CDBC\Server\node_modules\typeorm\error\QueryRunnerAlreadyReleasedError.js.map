{"version": 3, "sources": ["../../src/error/QueryRunnerAlreadyReleasedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C,MAAa,+BAAgC,SAAQ,2BAAY;IAC7D;QACI,KAAK,CAAC,4DAA4D,CAAC,CAAA;IACvE,CAAC;CACJ;AAJD,0EAIC", "file": "QueryRunnerAlreadyReleasedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class QueryRunnerAlreadyReleasedError extends TypeORMError {\n    constructor() {\n        super(`Query runner already released. Cannot run queries anymore.`)\n    }\n}\n"], "sourceRoot": ".."}