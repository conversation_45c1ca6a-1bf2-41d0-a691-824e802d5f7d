{"version": 3, "sources": ["../../src/error/PersistedEntityNotFoundError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,4BAA6B,SAAQ,2BAAY;IAC1D;QACI,KAAK,CACD,2FAA2F,CAC9F,CAAA;IACL,CAAC;CACJ;AAND,oEAMC", "file": "PersistedEntityNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown . Theoretically can't be thrown.\n */\nexport class PersistedEntityNotFoundError extends TypeORMError {\n    constructor() {\n        super(\n            `Internal error. Persisted entity was not found in the list of prepared operated entities.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}