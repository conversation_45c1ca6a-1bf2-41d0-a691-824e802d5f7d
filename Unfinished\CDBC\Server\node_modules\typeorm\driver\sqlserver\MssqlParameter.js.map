{"version": 3, "sources": ["../../src/driver/sqlserver/MssqlParameter.ts"], "names": [], "mappings": ";;;AAAA;;;;GAIG;AACH,MAAa,cAAc;IA8CvB,YAAmB,KAAU,EAAS,IAAY,EAAE,GAAG,MAAgB;QAApD,UAAK,GAAL,KAAK,CAAK;QAAS,SAAI,GAAJ,IAAI,CAAQ;QA7CzC,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QAErD,4EAA4E;QAC5E,oBAAoB;QACpB,4EAA4E;QAErE,WAAM,GAAU,EAAE,CAAA;QAwCrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAA;IAC9B,CAAC;CACJ;AAjDD,wCAiDC", "file": "MssqlParameter.js", "sourcesContent": ["/**\n * Sql server driver requires parameter types to be specified fo input parameters used in the query.\n *\n * @see https://github.com/patriksimek/node-mssql#data-types\n */\nexport class MssqlParameter {\n    readonly \"@instanceof\" = Symbol.for(\"MssqlParameter\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    public params: any[] = []\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(value: any, type: \"bit\")\n    constructor(value: any, type: \"bigint\")\n    constructor(value: any, type: \"decimal\", precision?: number, scale?: number)\n    constructor(value: any, type: \"float\")\n    constructor(value: any, type: \"int\")\n    constructor(value: any, type: \"money\")\n    constructor(value: any, type: \"numeric\", precision?: number, scale?: number)\n    constructor(value: any, type: \"smallint\")\n    constructor(value: any, type: \"smallmoney\")\n    constructor(value: any, type: \"real\")\n    constructor(value: any, type: \"tinyint\")\n    constructor(value: any, type: \"char\", length?: number)\n    constructor(value: any, type: \"nchar\", length?: number)\n    constructor(value: any, type: \"text\")\n    constructor(value: any, type: \"ntext\")\n    constructor(value: any, type: \"varchar\", length?: number)\n    constructor(value: any, type: \"nvarchar\", length?: number)\n    constructor(value: any, type: \"xml\")\n    constructor(value: any, type: \"time\", scale?: number)\n    constructor(value: any, type: \"date\")\n    constructor(value: any, type: \"datetime\")\n    constructor(value: any, type: \"datetime2\", scale?: number)\n    constructor(value: any, type: \"datetimeoffset\", scale?: number)\n    constructor(value: any, type: \"smalldatetime\")\n    constructor(value: any, type: \"uniqueidentifier\")\n    constructor(value: any, type: \"variant\")\n    constructor(value: any, type: \"binary\")\n    constructor(value: any, type: \"varbinary\", length?: number)\n    constructor(value: any, type: \"image\")\n    constructor(value: any, type: \"udt\")\n    constructor(value: any, type: \"geography\")\n    constructor(value: any, type: \"geometry\")\n    constructor(value: any, type: \"rowversion\")\n    constructor(public value: any, public type: string, ...params: number[]) {\n        this.params = params || []\n    }\n}\n"], "sourceRoot": "../.."}