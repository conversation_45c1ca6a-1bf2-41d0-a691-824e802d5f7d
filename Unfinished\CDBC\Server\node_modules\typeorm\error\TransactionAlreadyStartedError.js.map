{"version": 3, "sources": ["../../src/error/TransactionAlreadyStartedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,8BAA+B,SAAQ,2BAAY;IAC5D;QACI,KAAK,CACD,6GAA6G,CAChH,CAAA;IACL,CAAC;CACJ;AAND,wEAMC", "file": "TransactionAlreadyStartedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when transaction is already started and user tries to run it again.\n */\nexport class TransactionAlreadyStartedError extends TypeORMError {\n    constructor() {\n        super(\n            `Transaction already started for the given connection, commit current transaction before starting a new one.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}