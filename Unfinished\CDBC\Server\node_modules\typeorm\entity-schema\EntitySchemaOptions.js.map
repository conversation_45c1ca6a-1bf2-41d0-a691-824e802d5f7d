{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaOptions.ts"], "names": [], "mappings": ";;;AAiBA;;GAEG;AACH,MAAa,mBAAmB;CAqH/B;AArHD,kDAqHC", "file": "EntitySchemaOptions.js", "sourcesContent": ["import {\n    DataSource,\n    EntitySchemaEmbeddedColumnOptions,\n    SelectQueryBuilder,\n} from \"..\"\nimport { EntitySchemaIndexOptions } from \"./EntitySchemaIndexOptions\"\nimport { EntitySchemaColumnOptions } from \"./EntitySchemaColumnOptions\"\nimport { EntitySchemaRelationOptions } from \"./EntitySchemaRelationOptions\"\nimport { OrderByCondition } from \"../find-options/OrderByCondition\"\nimport { TableType } from \"../metadata/types/TableTypes\"\nimport { EntitySchemaUniqueOptions } from \"./EntitySchemaUniqueOptions\"\nimport { EntitySchemaCheckOptions } from \"./EntitySchemaCheckOptions\"\nimport { EntitySchemaExclusionOptions } from \"./EntitySchemaExclusionOptions\"\nimport { EntitySchemaInheritanceOptions } from \"./EntitySchemaInheritanceOptions\"\nimport { EntitySchemaRelationIdOptions } from \"./EntitySchemaRelationIdOptions\"\nimport { EntitySchemaForeignKeyOptions } from \"./EntitySchemaForeignKeyOptions\"\n\n/**\n * Interface for entity metadata mappings stored inside \"schemas\" instead of models decorated by decorators.\n */\nexport class EntitySchemaOptions<T> {\n    /**\n     * Target bind to this entity schema. Optional.\n     */\n    target?: Function\n\n    /**\n     * Entity name.\n     */\n    name: string\n\n    /**\n     * Table name.\n     */\n    tableName?: string\n\n    /**\n     * Database name. Used in MySql and Sql Server.\n     */\n    database?: string\n\n    /**\n     * Schema name. Used in Postgres and Sql Server.\n     */\n    schema?: string\n\n    /**\n     * Table type.\n     */\n    type?: TableType\n\n    /**\n     * Specifies a property name by which queries will perform ordering by default when fetching rows.\n     */\n    orderBy?: OrderByCondition\n\n    /**\n     * Entity column's options.\n     */\n    columns: {\n        [P in keyof T]?: EntitySchemaColumnOptions\n    }\n\n    /**\n     * Entity relation's options.\n     */\n    relations?: {\n        [P in keyof T]?: EntitySchemaRelationOptions\n    }\n\n    /**\n     * Entity relation id options.\n     */\n    relationIds?: {\n        [P in keyof T]?: EntitySchemaRelationIdOptions\n    }\n\n    /**\n     * Entity indices options.\n     */\n    indices?: EntitySchemaIndexOptions[]\n\n    /**\n     * Entity foreign keys options.\n     */\n    foreignKeys?: EntitySchemaForeignKeyOptions[]\n\n    /**\n     * Entity uniques options.\n     */\n    uniques?: EntitySchemaUniqueOptions[]\n\n    /**\n     * Entity check options.\n     */\n    checks?: EntitySchemaCheckOptions[]\n\n    /**\n     * Entity exclusion options.\n     */\n    exclusions?: EntitySchemaExclusionOptions[]\n\n    /**\n     * Embedded Entities options\n     */\n    embeddeds?: {\n        [P in keyof Partial<T>]: EntitySchemaEmbeddedColumnOptions\n    }\n\n    /**\n     * Indicates if schema synchronization is enabled or disabled for this entity.\n     * If it will be set to false then schema sync will and migrations ignore this entity.\n     * By default schema synchronization is enabled for all entities.\n     */\n    synchronize?: boolean\n\n    /**\n     * If set to 'true' this option disables Sqlite's default behaviour of secretly creating\n     * an integer primary key column named 'rowid' on table creation.\n     * @see https://www.sqlite.org/withoutrowid.html.\n     */\n    withoutRowid?: boolean\n\n    /**\n     * View expression.\n     */\n    expression?: string | ((connection: DataSource) => SelectQueryBuilder<any>)\n\n    /**\n     * Inheritance options.\n     */\n    inheritance?: EntitySchemaInheritanceOptions\n\n    /**\n     * Custom discriminator value for Single Table Inheritance.\n     */\n    discriminatorValue?: string\n}\n"], "sourceRoot": ".."}