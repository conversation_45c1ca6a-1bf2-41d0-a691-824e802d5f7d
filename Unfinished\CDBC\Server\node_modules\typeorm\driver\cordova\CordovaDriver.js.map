{"version": 3, "sources": ["../../src/driver/cordova/CordovaDriver.ts"], "names": [], "mappings": ";;;AAAA,kFAA8E;AAE9E,6DAAyD;AAGzD,+FAA2F;AAU3F,MAAa,aAAc,SAAQ,2CAAoB;IAKnD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QAPrB,uBAAkB,GAAG,MAAe,CAAA;QAShC,gCAAgC;QAChC,iEAAiE;QACjE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAErC,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAE5B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,IAAI,uCAAkB,CAAC,IAAI,CAAC,CAAA;QAEtE,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACpC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CACzB,EAAE,EACF;YACI,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC3B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;SAClC,EACD,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAC3B,CAAA;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;YACxD,IAAI,CAAC,MAAM,CAAC,YAAY,CACpB,OAAO,EACP,CAAC,EAAO,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,EACxB,CAAC,GAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAC1B,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACjC,yFAAyF;YACzF,kEAAkE;YAClE,UAAU,CAAC,UAAU,CACjB,0BAA0B,EAC1B,EAAE,EACF,GAAG,EAAE,CAAC,EAAE,EAAE,EACV,CAAC,GAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAC1B,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,OAAO,UAAU,CAAA;IACrB,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,CAAA;YACzD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACxB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,+DAA8B,CACpC,gBAAgB,EAChB,wBAAwB,CAC3B,CAAA;QACL,CAAC;IACL,CAAC;CACJ;AAjGD,sCAiGC", "file": "CordovaDriver.js", "sourcesContent": ["import { AbstractSqliteDriver } from \"../sqlite-abstract/AbstractSqliteDriver\"\nimport { CordovaConnectionOptions } from \"./CordovaConnectionOptions\"\nimport { CordovaQueryRunner } from \"./CordovaQueryRunner\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\n\n// needed for typescript compiler\ninterface Window {\n    sqlitePlugin: any\n}\n\ndeclare let window: Window\n\nexport class CordovaDriver extends AbstractSqliteDriver {\n    options: CordovaConnectionOptions\n\n    transactionSupport = \"none\" as const\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n\n        // this.connection = connection;\n        // this.options = connection.options as CordovaConnectionOptions;\n        this.database = this.options.database\n\n        // load sqlite package\n        this.loadDependencies()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        this.queryRunner = undefined\n\n        return new Promise<void>((ok, fail) => {\n            this.databaseConnection.close(ok, fail)\n        })\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner) this.queryRunner = new CordovaQueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     */\n    protected async createDatabaseConnection() {\n        const options = Object.assign(\n            {},\n            {\n                name: this.options.database,\n                location: this.options.location,\n            },\n            this.options.extra || {},\n        )\n\n        const connection = await new Promise<any>((resolve, fail) => {\n            this.sqlite.openDatabase(\n                options,\n                (db: any) => resolve(db),\n                (err: any) => fail(err),\n            )\n        })\n\n        await new Promise<void>((ok, fail) => {\n            // we need to enable foreign keys in sqlite to make sure all foreign key related features\n            // working properly. this also makes onDelete to work with sqlite.\n            connection.executeSql(\n                `PRAGMA foreign_keys = ON`,\n                [],\n                () => ok(),\n                (err: any) => fail(err),\n            )\n        })\n\n        return connection\n    }\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const sqlite = this.options.driver || window.sqlitePlugin\n            this.sqlite = sqlite\n        } catch (e) {\n            throw new DriverPackageNotInstalledError(\n                \"Cordova-SQLite\",\n                \"cordova-sqlite-storage\",\n            )\n        }\n    }\n}\n"], "sourceRoot": "../.."}