{"version": 3, "sources": ["../../src/error/OptimisticLockCanNotBeUsedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,+BAAgC,SAAQ,2BAAY;IAC7D;QACI,KAAK,CAAC,4DAA4D,CAAC,CAAA;IACvE,CAAC;CACJ;AAJD,0EAIC", "file": "OptimisticLockCanNotBeUsedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when an optimistic lock cannot be used in query builder.\n */\nexport class OptimisticLockCanNotBeUsedError extends TypeORMError {\n    constructor() {\n        super(`The optimistic lock can be used only with getOne() method.`)\n    }\n}\n"], "sourceRoot": ".."}