{"version": 3, "sources": ["../../src/decorator/options/ColumnCommonOptions.ts"], "names": [], "mappings": "", "file": "ColumnCommonOptions.js", "sourcesContent": ["import { ValueTransformer } from \"./ValueTransformer\"\n\n/**\n * Column options specific to all column types.\n */\nexport interface ColumnCommonOptions {\n    /**\n     * Indicates if column is always selected by QueryBuilder and find operations.\n     * Default value is \"true\".\n     */\n    select?: boolean\n\n    /**\n     * Column name in the database.\n     */\n    name?: string\n\n    /**\n     * Indicates if this column is a primary key.\n     * Same can be achieved when @PrimaryColumn decorator is used.\n     */\n    primary?: boolean\n\n    /**\n     * Specifies if this column will use auto increment (sequence, generated identity, rowid).\n     * Note that in some databases only one column in entity can be marked as generated, and it must be a primary column.\n     */\n    generated?: boolean | \"increment\" | \"uuid\" | \"rowid\" | \"identity\"\n\n    /**\n     * Specifies if column's value must be unique or not.\n     */\n    unique?: boolean\n\n    /**\n     * Indicates if column's value can be set to NULL.\n     */\n    nullable?: boolean\n\n    /**\n     * Default database value.\n     * Note that default value is not supported when column type is 'json' of mysql.\n     */\n    default?: any\n\n    /**\n     * ON UPDATE trigger. Works only for MySQL.\n     */\n    onUpdate?: string\n\n    /**\n     * Column comment. Not supported by all database types.\n     */\n    comment?: string\n\n    /**\n     * Indicates if this column is an array.\n     * Can be simply set to true or array length can be specified.\n     * Supported only by postgres.\n     */\n    array?: boolean\n\n    /**\n     * Specifies a value transformer that is to be used to (un)marshal\n     * this column when reading or writing to the database.\n     */\n    transformer?: ValueTransformer | ValueTransformer[]\n}\n"], "sourceRoot": "../.."}