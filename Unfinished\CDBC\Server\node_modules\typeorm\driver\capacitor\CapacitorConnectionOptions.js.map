{"version": 3, "sources": ["../../src/driver/capacitor/CapacitorConnectionOptions.ts"], "names": [], "mappings": "", "file": "CapacitorConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Sqlite-specific connection options.\n */\nexport interface CapacitorConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"capacitor\"\n\n    /**\n     * The capacitor-sqlite instance. For example, `new SQLiteConnection(CapacitorSQLite)`.\n     */\n    readonly driver: any\n\n    /**\n     * Database name (capacitor-sqlite will add the suffix `SQLite.db`)\n     */\n    readonly database: string\n\n    /**\n     * Set the mode for database encryption\n     */\n    readonly mode?: \"no-encryption\" | \"encryption\" | \"secret\" | \"newsecret\"\n\n    /**\n     * Database version\n     */\n    readonly version?: number\n\n    /**\n     * The SQLite journal mode (optional)\n     */\n    readonly journalMode?:\n        | \"DELETE\"\n        | \"TRUNCATE\"\n        | \"PERSIST\"\n        | \"MEMORY\"\n        | \"WAL\"\n        | \"OFF\"\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}