{"version": 3, "sources": ["../../src/error/MissingPrimaryColumnError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAE7C,MAAa,yBAA0B,SAAQ,2BAAY;IACvD,YAAY,cAA8B;QACtC,KAAK,CACD,WAAW,cAAc,CAAC,IAAI,kEAAkE;YAC5F,iGAAiG,CACxG,CAAA;IACL,CAAC;CACJ;AAPD,8DAOC", "file": "MissingPrimaryColumnError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class MissingPrimaryColumnError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata) {\n        super(\n            `Entity \"${entityMetadata.name}\" does not have a primary column. Primary column is required to ` +\n                `have in all your entities. Use @PrimaryColumn decorator to add a primary column to your entity.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}