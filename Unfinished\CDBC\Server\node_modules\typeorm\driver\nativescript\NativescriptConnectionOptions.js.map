{"version": 3, "sources": ["../../src/driver/nativescript/NativescriptConnectionOptions.ts"], "names": [], "mappings": "", "file": "NativescriptConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * NativeScript-specific connection options.\n */\nexport interface NativescriptConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"nativescript\"\n\n    /**\n     * Database name.\n     */\n    readonly database: string\n\n    /**\n     * The driver object\n     * you should pass `require('nativescript-sqlite') here\n     */\n    readonly driver: any\n\n    /**\n     * Whether to mark the mark the database as read only on open (iOS only).\n     */\n    readonly readOnly?: boolean\n\n    /**\n     * The key to use for for using/opening encrypted databases. (requires the \"Encrypted Plugin\")\n     */\n    readonly key?: string\n\n    /**\n     * Whether to enable background multitasking. All SQL is ran on a background worker thread. (requires the \"Commercial Plugin\")\n     */\n    readonly multithreading?: boolean\n\n    /**\n     * Migrates a Encrypted Sql database from v3 to the new v4. If you are a new user you do not need to set this flag as new created databases will already be in v4.\n     * If you are upgrading a app that used v1.3.0 or earlier of NS-Sqlite-Encrypted; then you will probably want to set this flag to true. (requires the \"Encrypted Plugin\")\n     */\n    readonly migrate?: boolean\n\n    /**\n     * Flags to pass to SQLite when opening the database on iOS. (see https://www.sqlite.org/c3ref/open.html)\n     */\n    readonly iosFlags?: number\n\n    /**\n     * Flags to pass to SQLite when opening the database on Android. (see https://developer.android.com/reference/android/database/sqlite/SQLiteDatabase.html)\n     */\n    readonly androidFlags?: number\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}