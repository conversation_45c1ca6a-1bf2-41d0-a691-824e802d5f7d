{"version": 3, "sources": ["../../src/error/DataTypeNotSupportedError.ts"], "names": [], "mappings": ";;;AAGA,iDAA6C;AAE7C,MAAa,yBAA0B,SAAQ,2BAAY;IACvD,YACI,MAAsB,EACtB,QAAoB,EACpB,QAAuB;QAEvB,KAAK,EAAE,CAAA;QAEP,MAAM,IAAI,GACN,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAO,QAAS,CAAC,IAAI,CAAA;QAClE,IAAI,CAAC,OAAO,GAAG,cAAc,IAAI,SAAS,MAAM,CAAC,cAAc,CAAC,UAAU,IAAI,MAAM,CAAC,YAAY,0BAA0B,QAAQ,aAAa,CAAA;IACpJ,CAAC;CACJ;AAZD,8DAYC", "file": "DataTypeNotSupportedError.js", "sourcesContent": ["import { ColumnType } from \"../driver/types/ColumnTypes\"\nimport { DatabaseType } from \"../driver/types/DatabaseType\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class DataTypeNotSupportedError extends TypeORMError {\n    constructor(\n        column: ColumnMetadata,\n        dataType: ColumnType,\n        database?: DatabaseType,\n    ) {\n        super()\n\n        const type =\n            typeof dataType === \"string\" ? dataType : (<any>dataType).name\n        this.message = `Data type \"${type}\" in \"${column.entityMetadata.targetName}.${column.propertyName}\" is not supported by \"${database}\" database.`\n    }\n}\n"], "sourceRoot": ".."}