{"version": 3, "sources": ["../../src/driver/types/DatabaseType.ts"], "names": [], "mappings": "", "file": "DatabaseType.js", "sourcesContent": ["/**\n * Database type.\n */\nexport type DatabaseType =\n    | \"mysql\"\n    | \"postgres\"\n    | \"cockroachdb\"\n    | \"sap\"\n    | \"mariadb\"\n    | \"sqlite\"\n    | \"cordova\"\n    | \"react-native\"\n    | \"nativescript\"\n    | \"sqljs\"\n    | \"oracle\"\n    | \"mssql\"\n    | \"mongodb\"\n    | \"aurora-mysql\"\n    | \"aurora-postgres\"\n    | \"expo\"\n    | \"better-sqlite3\"\n    | \"capacitor\"\n    | \"spanner\"\n"], "sourceRoot": "../.."}