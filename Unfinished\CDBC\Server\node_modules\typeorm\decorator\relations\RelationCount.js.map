{"version": 3, "sources": ["../../src/decorator/relations/RelationCount.ts"], "names": [], "mappings": ";;AAUA,sCAgBC;AA1BD,2CAAsD;AAItD;;;;;GAKG;AACH,SAAgB,aAAa,CACzB,QAAuC,EACvC,KAAc,EACd,mBAE4B;IAE5B,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAA,gCAAsB,GAAE,CAAC,cAAc,CAAC,IAAI,CAAC;YACzC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE,KAAK;YACZ,mBAAmB,EAAE,mBAAmB;SACd,CAAC,CAAA;IACnC,CAAC,CAAA;AACL,CAAC", "file": "RelationCount.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { RelationCountMetadataArgs } from \"../../metadata-args/RelationCountMetadataArgs\"\nimport { SelectQueryBuilder } from \"../../query-builder/SelectQueryBuilder\"\n\n/**\n * Holds a number of children in the closure table of the column.\n *\n * @deprecated This decorator will removed in the future versions.\n * Use {@link VirtualColumn} to calculate the count instead.\n */\nexport function RelationCount<T>(\n    relation: string | ((object: T) => any),\n    alias?: string,\n    queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>,\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().relationCounts.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            relation: relation,\n            alias: alias,\n            queryBuilderFactory: queryBuilderFactory,\n        } as RelationCountMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}