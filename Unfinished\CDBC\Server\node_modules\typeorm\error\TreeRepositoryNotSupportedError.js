"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TreeRepositoryNotSupportedError = void 0;
const TypeORMError_1 = require("./TypeORMError");
class TreeRepositoryNotSupportedError extends TypeORMError_1.TypeORMError {
    constructor(driver) {
        super(`Tree repositories are not supported in ${driver.options.type} driver.`);
    }
}
exports.TreeRepositoryNotSupportedError = TreeRepositoryNotSupportedError;

//# sourceMappingURL=TreeRepositoryNotSupportedError.js.map
