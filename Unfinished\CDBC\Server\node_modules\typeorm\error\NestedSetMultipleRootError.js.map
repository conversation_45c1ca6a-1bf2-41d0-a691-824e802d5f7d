{"version": 3, "sources": ["../../src/error/NestedSetMultipleRootError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C,MAAa,0BAA2B,SAAQ,2BAAY;IACxD;QACI,KAAK,CAAC,oDAAoD,CAAC,CAAA;IAC/D,CAAC;CACJ;AAJD,gEAIC", "file": "NestedSetMultipleRootError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class NestedSetMultipleRootError extends TypeORMError {\n    constructor() {\n        super(`Nested sets do not support multiple root entities.`)\n    }\n}\n"], "sourceRoot": ".."}