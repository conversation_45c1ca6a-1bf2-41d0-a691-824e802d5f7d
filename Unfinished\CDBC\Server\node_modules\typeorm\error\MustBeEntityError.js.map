{"version": 3, "sources": ["../../src/error/MustBeEntityError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,iBAAkB,SAAQ,2BAAY;IAC/C,YAAY,SAAiB,EAAE,UAAe;QAC1C,KAAK,CACD,UAAU,SAAS,6CAA6C,UAAU,aAAa,CAC1F,CAAA;IACL,CAAC;CACJ;AAND,8CAMC", "file": "MustBeEntityError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when method expects entity but instead something else is given.\n */\nexport class MustBeEntityError extends TypeORMError {\n    constructor(operation: string, wrongValue: any) {\n        super(\n            `Cannot ${operation}, given value must be an entity, instead \"${wrongValue}\" is given.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}