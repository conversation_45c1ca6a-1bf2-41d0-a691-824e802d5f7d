{"version": 3, "sources": ["../../src/decorator/Check.ts"], "names": [], "mappings": ";;AA0BA,sBAqBC;AA/CD,wCAAmD;AAEnD,oCAAuC;AAmBvC;;;;GAIG;AACH,SAAgB,KAAK,CACjB,gBAAwB,EACxB,eAAwB;IAExB,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAA;IAC3D,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAA;IAEvE,IAAI,CAAC,UAAU;QAAE,MAAM,IAAI,oBAAY,CAAC,8BAA8B,CAAC,CAAA;IAEvE,OAAO,UACH,WAA8B,EAC9B,YAA8B;QAE9B,IAAA,gCAAsB,GAAE,CAAC,MAAM,CAAC,IAAI,CAAC;YACjC,MAAM,EAAE,YAAY;gBAChB,CAAC,CAAC,WAAW,CAAC,WAAW;gBACzB,CAAC,CAAE,WAAwB;YAC/B,IAAI,EAAE,IAAI;YACV,UAAU,EAAE,UAAU;SACJ,CAAC,CAAA;IAC3B,CAAC,CAAA;AACL,CAAC", "file": "Check.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../globals\"\nimport { CheckMetadataArgs } from \"../metadata-args/CheckMetadataArgs\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Creates a database check.\n * Can be used on entity property or on entity.\n * Can create checks with composite columns when used on entity.\n */\nexport function Check(expression: string): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database check.\n * Can be used on entity property or on entity.\n * Can create checks with composite columns when used on entity.\n */\nexport function Check(\n    name: string,\n    expression: string,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database check.\n * Can be used on entity property or on entity.\n * Can create checks with composite columns when used on entity.\n */\nexport function Check(\n    nameOrExpression: string,\n    maybeExpression?: string,\n): ClassDecorator & PropertyDecorator {\n    const name = maybeExpression ? nameOrExpression : undefined\n    const expression = maybeExpression ? maybeExpression : nameOrExpression\n\n    if (!expression) throw new TypeORMError(`Check expression is required`)\n\n    return function (\n        clsOrObject: Function | Object,\n        propertyName?: string | symbol,\n    ) {\n        getMetadataArgsStorage().checks.push({\n            target: propertyName\n                ? clsOrObject.constructor\n                : (clsOrObject as Function),\n            name: name,\n            expression: expression,\n        } as CheckMetadataArgs)\n    }\n}\n"], "sourceRoot": ".."}