{"version": 3, "sources": ["../../src/driver/DriverFactory.ts"], "names": [], "mappings": ";;;AAAA,oEAAgE;AAChE,mEAA+D;AAC/D,uDAAmD;AACnD,iEAA6D;AAC7D,wDAAoD;AACpD,wDAAoD;AACpD,2DAAuD;AACvD,wEAAoE;AACpE,0EAAsE;AACtE,qDAAiD;AACjD,qDAAiD;AACjD,8DAA0D;AAC1D,gEAA4D;AAC5D,wEAAoE;AACpE,iFAA6E;AAG7E,+CAA2C;AAC3C,8EAA0E;AAC1E,iEAA6D;AAC7D,2DAAuD;AAEvD;;GAEG;AACH,MAAa,aAAa;IACtB;;OAEG;IACH,MAAM,CAAC,UAAsB;QACzB,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,OAAO,CAAA;QACnC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,OAAO;gBACR,OAAO,IAAI,yBAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,UAAU;gBACX,OAAO,IAAI,+BAAc,CAAC,UAAU,CAAC,CAAA;YACzC,KAAK,aAAa;gBACd,OAAO,IAAI,iCAAe,CAAC,UAAU,CAAC,CAAA;YAC1C,KAAK,KAAK;gBACN,OAAO,IAAI,qBAAS,CAAC,UAAU,CAAC,CAAA;YACpC,KAAK,SAAS;gBACV,OAAO,IAAI,yBAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,QAAQ;gBACT,OAAO,IAAI,2BAAY,CAAC,UAAU,CAAC,CAAA;YACvC,KAAK,gBAAgB;gBACjB,OAAO,IAAI,yCAAmB,CAAC,UAAU,CAAC,CAAA;YAC9C,KAAK,SAAS;gBACV,OAAO,IAAI,6BAAa,CAAC,UAAU,CAAC,CAAA;YACxC,KAAK,cAAc;gBACf,OAAO,IAAI,uCAAkB,CAAC,UAAU,CAAC,CAAA;YAC7C,KAAK,cAAc;gBACf,OAAO,IAAI,qCAAiB,CAAC,UAAU,CAAC,CAAA;YAC5C,KAAK,OAAO;gBACR,OAAO,IAAI,yBAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,QAAQ;gBACT,OAAO,IAAI,2BAAY,CAAC,UAAU,CAAC,CAAA;YACvC,KAAK,OAAO;gBACR,OAAO,IAAI,iCAAe,CAAC,UAAU,CAAC,CAAA;YAC1C,KAAK,SAAS;gBACV,OAAO,IAAI,yBAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,MAAM;gBACP,OAAO,IAAI,qCAAiB,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAA;YACrD,KAAK,cAAc;gBACf,OAAO,IAAI,qCAAiB,CAAC,UAAU,CAAC,CAAA;YAC5C,KAAK,iBAAiB;gBAClB,OAAO,IAAI,2CAAoB,CAAC,UAAU,CAAC,CAAA;YAC/C,KAAK,WAAW;gBACZ,OAAO,IAAI,iCAAe,CAAC,UAAU,CAAC,CAAA;YAC1C,KAAK,SAAS;gBACV,OAAO,IAAI,6BAAa,CAAC,UAAU,CAAC,CAAA;YACxC;gBACI,MAAM,IAAI,uCAAkB,CAAC,IAAI,EAAE;oBAC/B,cAAc;oBACd,iBAAiB;oBACjB,gBAAgB;oBAChB,WAAW;oBACX,aAAa;oBACb,SAAS;oBACT,MAAM;oBACN,SAAS;oBACT,SAAS;oBACT,OAAO;oBACP,OAAO;oBACP,cAAc;oBACd,QAAQ;oBACR,UAAU;oBACV,cAAc;oBACd,KAAK;oBACL,QAAQ;oBACR,OAAO;oBACP,SAAS;iBACZ,CAAC,CAAA;QACV,CAAC;IACL,CAAC;CACJ;AArED,sCAqEC", "file": "DriverFactory.js", "sourcesContent": ["import { MissingDriverError } from \"../error/MissingDriverError\"\nimport { CockroachDriver } from \"./cockroachdb/CockroachDriver\"\nimport { MongoDriver } from \"./mongodb/MongoDriver\"\nimport { SqlServerDriver } from \"./sqlserver/SqlServerDriver\"\nimport { OracleDriver } from \"./oracle/OracleDriver\"\nimport { SqliteDriver } from \"./sqlite/SqliteDriver\"\nimport { CordovaDriver } from \"./cordova/CordovaDriver\"\nimport { ReactNativeDriver } from \"./react-native/ReactNativeDriver\"\nimport { NativescriptDriver } from \"./nativescript/NativescriptDriver\"\nimport { SqljsDriver } from \"./sqljs/SqljsDriver\"\nimport { MysqlDriver } from \"./mysql/MysqlDriver\"\nimport { PostgresDriver } from \"./postgres/PostgresDriver\"\nimport { ExpoDriverFactory } from \"./expo/ExpoDriverFactory\"\nimport { AuroraMysqlDriver } from \"./aurora-mysql/AuroraMysqlDriver\"\nimport { AuroraPostgresDriver } from \"./aurora-postgres/AuroraPostgresDriver\"\nimport { Driver } from \"./Driver\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { SapDriver } from \"./sap/SapDriver\"\nimport { BetterSqlite3Driver } from \"./better-sqlite3/BetterSqlite3Driver\"\nimport { CapacitorDriver } from \"./capacitor/CapacitorDriver\"\nimport { SpannerDriver } from \"./spanner/SpannerDriver\"\n\n/**\n * Helps to create drivers.\n */\nexport class DriverFactory {\n    /**\n     * Creates a new driver depend on a given connection's driver type.\n     */\n    create(connection: DataSource): Driver {\n        const { type } = connection.options\n        switch (type) {\n            case \"mysql\":\n                return new MysqlDriver(connection)\n            case \"postgres\":\n                return new PostgresDriver(connection)\n            case \"cockroachdb\":\n                return new CockroachDriver(connection)\n            case \"sap\":\n                return new SapDriver(connection)\n            case \"mariadb\":\n                return new MysqlDriver(connection)\n            case \"sqlite\":\n                return new SqliteDriver(connection)\n            case \"better-sqlite3\":\n                return new BetterSqlite3Driver(connection)\n            case \"cordova\":\n                return new CordovaDriver(connection)\n            case \"nativescript\":\n                return new NativescriptDriver(connection)\n            case \"react-native\":\n                return new ReactNativeDriver(connection)\n            case \"sqljs\":\n                return new SqljsDriver(connection)\n            case \"oracle\":\n                return new OracleDriver(connection)\n            case \"mssql\":\n                return new SqlServerDriver(connection)\n            case \"mongodb\":\n                return new MongoDriver(connection)\n            case \"expo\":\n                return new ExpoDriverFactory(connection).create()\n            case \"aurora-mysql\":\n                return new AuroraMysqlDriver(connection)\n            case \"aurora-postgres\":\n                return new AuroraPostgresDriver(connection)\n            case \"capacitor\":\n                return new CapacitorDriver(connection)\n            case \"spanner\":\n                return new SpannerDriver(connection)\n            default:\n                throw new MissingDriverError(type, [\n                    \"aurora-mysql\",\n                    \"aurora-postgres\",\n                    \"better-sqlite3\",\n                    \"capacitor\",\n                    \"cockroachdb\",\n                    \"cordova\",\n                    \"expo\",\n                    \"mariadb\",\n                    \"mongodb\",\n                    \"mssql\",\n                    \"mysql\",\n                    \"nativescript\",\n                    \"oracle\",\n                    \"postgres\",\n                    \"react-native\",\n                    \"sap\",\n                    \"sqlite\",\n                    \"sqljs\",\n                    \"spanner\",\n                ])\n        }\n    }\n}\n"], "sourceRoot": ".."}