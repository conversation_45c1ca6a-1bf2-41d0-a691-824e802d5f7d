{"version": 3, "sources": ["../../src/error/CustomRepositoryNotFoundError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,6BAA8B,SAAQ,2BAAY;IAC3D,YAAY,UAAe;QACvB,KAAK,CACD,qBACI,OAAO,UAAU,KAAK,UAAU;YAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,IACjC,kBAAkB;YACd,0DAA0D,CACjE,CAAA;IACL,CAAC;CACJ;AAXD,sEAWC", "file": "CustomRepositoryNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown if custom repository was not found.\n */\nexport class CustomRepositoryNotFoundError extends TypeORMError {\n    constructor(repository: any) {\n        super(\n            `Custom repository ${\n                typeof repository === \"function\"\n                    ? repository.name\n                    : repository.constructor.name\n            } was not found. ` +\n                `Did you forgot to put @EntityRepository decorator on it?`,\n        )\n    }\n}\n"], "sourceRoot": ".."}