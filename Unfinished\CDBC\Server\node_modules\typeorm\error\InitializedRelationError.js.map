{"version": 3, "sources": ["../../src/error/InitializedRelationError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAE7C;;;;;GAKG;AACH,MAAa,wBAAyB,SAAQ,2BAAY;IACtD,YAAY,QAA0B;QAClC,KAAK,CACD,6DAA6D;YACzD,mDAAmD,QAAQ,CAAC,cAAc,CAAC,UAAU,IAAI,QAAQ,CAAC,YAAY,KAAK;YACnH,8FAA8F,CACrG,CAAA;IACL,CAAC;CACJ;AARD,4DAQC", "file": "InitializedRelationError.js", "sourcesContent": ["import { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when relation has array initialized which is forbidden my ORM.\n *\n * @see https://github.com/typeorm/typeorm/issues/1319\n * @see http://typeorm.io/docs/Relations/relations-faq/#avoid-relation-property-initializers\n */\nexport class InitializedRelationError extends TypeORMError {\n    constructor(relation: RelationMetadata) {\n        super(\n            `Array initializations are not allowed in entity relations. ` +\n                `Please remove array initialization (= []) from \"${relation.entityMetadata.targetName}#${relation.propertyPath}\". ` +\n                `This is ORM requirement to make relations to work properly. Refer docs for more information.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}