{"version": 3, "sources": ["../../src/error/CannotReflectMethodParameterTypeError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;;GAGG;AACH,MAAa,qCAAsC,SAAQ,2BAAY;IACnE,YAAY,MAAgB,EAAE,UAAkB;QAC5C,KAAK,CACD,oCAAoC,UAAU,4BAA4B,MAAM,CAAC,IAAI,WAAW;YAC5F,yFAAyF;YACzF,wGAAwG,CAC/G,CAAA;IACL,CAAC;CACJ;AARD,sFAQC", "file": "CannotReflectMethodParameterTypeError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when ORM cannot get method parameter's type.\n * Basically, when reflect-metadata is not available or tsconfig is not properly setup.\n */\nexport class CannotReflectMethodParameterTypeError extends TypeORMError {\n    constructor(target: Function, methodName: string) {\n        super(\n            `Cannot get reflected type for a \"${methodName}\" method's parameter of \"${target.name}\" class. ` +\n                `Make sure you have turned on an \"emitDecoratorMetadata\": true option in tsconfig.json. ` +\n                `Also make sure you have imported \"reflect-metadata\" on top of the main entry file in your application.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}