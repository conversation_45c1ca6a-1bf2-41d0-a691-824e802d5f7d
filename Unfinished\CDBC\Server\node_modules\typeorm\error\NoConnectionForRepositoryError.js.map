{"version": 3, "sources": ["../../src/error/NoConnectionForRepositoryError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,8BAA+B,SAAQ,2BAAY;IAC5D,YAAY,cAAsB;QAC9B,KAAK,CACD,gCAAgC,cAAc,oDAAoD;YAC9F,iFAAiF,CACxF,CAAA;IACL,CAAC;CACJ;AAPD,wEAOC", "file": "NoConnectionForRepositoryError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to access repository before connection is established.\n */\nexport class NoConnectionForRepositoryError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot get a Repository for \"${connectionName} connection, because connection with the database ` +\n                `is not established yet. Call connection#connect method to establish connection.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}