{"version": 3, "sources": ["../../src/error/CannotAttachTreeChildrenEntityError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,mCAAoC,SAAQ,2BAAY;IACjE,YAAY,UAAkB;QAC1B,KAAK,CACD,yBAAyB,UAAU,2CAA2C;YAC1E,wDAAwD,CAC/D,CAAA;IACL,CAAC;CACJ;AAPD,kFAOC", "file": "CannotAttachTreeChildrenEntityError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user saves tree children entity but its parent is not saved yet.\n */\nexport class CannotAttachTreeChildrenEntityError extends TypeORMError {\n    constructor(entityName: string) {\n        super(\n            `Cannot attach entity \"${entityName}\" to its parent. Please make sure parent ` +\n                `is saved in the database before saving children nodes.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}