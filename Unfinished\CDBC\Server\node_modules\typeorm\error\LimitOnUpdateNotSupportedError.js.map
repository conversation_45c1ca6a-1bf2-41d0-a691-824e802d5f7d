{"version": 3, "sources": ["../../src/error/LimitOnUpdateNotSupportedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AAEH,MAAa,8BAA+B,SAAQ,2BAAY;IAC5D;QACI,KAAK,CAAC,4DAA4D,CAAC,CAAA;IACvE,CAAC;CACJ;AAJD,wEAIC", "file": "LimitOnUpdateNotSupportedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to build an UPDATE query with LIMIT but the database does not support it.\n */\n\nexport class LimitOnUpdateNotSupportedError extends TypeORMError {\n    constructor() {\n        super(`Your database does not support LIMIT on UPDATE statements.`)\n    }\n}\n"], "sourceRoot": ".."}